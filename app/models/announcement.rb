# == Schema Information
#
# Table name: announcements
#
#  id            :bigint           not null, primary key
#  message       :text             not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  created_by_id :bigint           not null
#
# Indexes
#
#  index_announcements_on_created_by_id  (created_by_id)
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#
class Announcement < ApplicationRecord
  belongs_to :created_by, class_name: 'User'

  validates :message, presence: true, length: { maximum: 7500 }
end
