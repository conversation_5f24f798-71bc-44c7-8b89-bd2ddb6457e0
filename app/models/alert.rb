# == Schema Information
#
# Table name: alerts
#
#  id                         :bigint           not null, primary key
#  alert_type                 :string
#  caution_issued             :boolean          default(FALSE)
#  closed_at                  :datetime
#  closed_user_organization   :string
#  closed_user_position_title :string
#  closure_note               :string
#  date                       :date
#  description                :text
#  discarded_at               :datetime
#  location                   :text
#  reported_by                :string
#  time                       :time
#  user_organization          :string
#  user_position_title        :string
#  witnesses                  :string           default([]), is an Array
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  closed_user_id             :bigint
#  motel_id                   :bigint           not null
#  user_id                    :bigint           not null
#
# Indexes
#
#  index_alerts_on_closed_user_id  (closed_user_id)
#  index_alerts_on_discarded_at    (discarded_at)
#  index_alerts_on_motel_id        (motel_id)
#  index_alerts_on_user_id         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (user_id => users.id)
#
class Alert < ApplicationRecord
  belongs_to :user
  belongs_to :motel
  belongs_to :closed_user, class_name: 'User', optional: true
  has_many :alert_logs, dependent: :destroy
  has_many :alert_actions, dependent: :destroy
  has_many :alert_histories, dependent: :destroy
  include Discard::Model

  accepts_nested_attributes_for :alert_actions, allow_destroy: true
  accepts_nested_attributes_for :alert_logs, allow_destroy: true
  accepts_nested_attributes_for :alert_histories, allow_destroy: true

  ALERT_TYPES = [
    'Category 1',
    'Category 2',
    'Category 3',
  ]

  def alert_type_color
    case alert_type
    when 'Category 1'
      '#760f0f'
    when 'Category 2'
      '#b62525'
    when 'Category 3'
      '#f55c59'
    end
  end

  validates :alert_type, presence: true, inclusion: { in: ALERT_TYPES }
  validates :date, presence: true
  validates :description, presence: true
  validates :location, presence: true
  validates :time, presence: true
  validates :user_organization, presence: true
  validates :user_position_title, presence: true
  validates :reported_by, presence: true
  validates :closure_note, presence: true, if: :closed?
  validates :closed_user_organization, :closed_user_position_title, presence: true, if: :closed?
  validates :closed_user_id, presence: true, if: :closed?

  scope :open, -> { where(discarded_at: nil, closed_at: nil) }
  scope :closed, -> { where("discarded_at IS NULL AND closed_at IS NOT NULL AND closed_at > ?", 2.weeks.ago) }
  scope :archived, -> { where("discarded_at IS NOT NULL OR closed_at < ?", 2.weeks.ago) }
  scope :not_archived, -> { where("discarded_at IS NULL AND (closed_at IS NULL OR closed_at >= ?)", 2.weeks.ago) }

  def archived_at
    return discarded_at if discarded_at.present?
    return closed_at + 2.weeks if closed_at.present?

    nil
  end

  def latest_history_at
    alert_histories.maximum(:created_at) || self.created_at
  end

  def alert_updated_date
    latest_history_at.to_date
  end

  def open?
    discarded_at.nil? && closed_at.nil?
  end

  def closed?
    closed_at.present?
  end

  def archived?
    discarded_at.present? || (closed_at.present? && closed_at < 2.weeks.ago)
  end

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization if user_organization.present?
    titles.join(" - ")
  end

  def closed_user_full_name
    closed_user&.full_name
  end

  def closed_user_position_organization
    titles = []
    titles << closed_user_position_title if closed_user_position_title.present?
    titles << closed_user_organization if closed_user_organization.present?
    titles.join(" - ")
  end
end
