# == Schema Information
#
# Table name: notification_preferences
#
#  id                         :bigint           not null, primary key
#  email_notification_allowed :boolean
#  notification_event         :integer
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  user_id                    :bigint           not null
#
# Indexes
#
#  index_notification_preferences_on_user_id            (user_id)
#  index_notification_preferences_on_user_id_and_event  (user_id,notification_event) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class NotificationPreference < ApplicationRecord
  belongs_to :user

  enum notification_event: {
    motel_created: 0,
    motel_updated: 1,
    motel_deactivated: 2,
    motel_reactivated: 3,

    ## Too important to be disabled
    # alert_created: 100,
    # alert_updated: 101,
    # alert_discarded: 102,

    caution_created: 200,
    caution_updated: 201,
    caution_discarded: 202,
    caution_undiscarded: 203,

    suggestion_created: 300,
    suggestion_updated: 301,
    suggestion_discarded: 302,
    suggestion_undiscarded: 303,

    comment_created: 400,
    comment_updated: 401,
    comment_discarded: 402,
    comment_undiscarded: 403

    # user_created: 500, # Not yet used
    # user_updated: 501, # Unused
    # user_locked: 502 # Not yet used

    ## Cannot be disabled
    # announcement_created: 600
  }

  validates :notification_event, uniqueness: { scope: [:user_id] }
  validates :email_notification_allowed, inclusion: [true, false]

  NOTIFICATION_GROUPS = {
    motel: [
      :motel_created,
      :motel_updated,
      :motel_deactivated,
      :motel_reactivated
    ],
    # alert: [
    #   :alert_created,
    #   :alert_updated,
    #   :alert_discarded
    # ],
    # announcement: [
    #  :announcement_created
    #  ],
    caution: [
      :caution_created,
      :caution_updated,
      :caution_discarded,
      :caution_undiscarded
    ],
    suggestion: [
      :suggestion_created,
      :suggestion_updated,
      :suggestion_discarded,
      :suggestion_undiscarded
    ],
    comment: [
      :comment_created,
      :comment_updated,
      :comment_discarded,
      :comment_undiscarded,
    ],
    # user: [
    # :user_created,
    # :user_updated,
    # :user_locked
    # ]
  }.freeze
end
