# == Schema Information
#
# Table name: alert_actions
#
#  id         :bigint           not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  alert_id   :bigint           not null
#
# Indexes
#
#  index_alert_actions_on_alert_id  (alert_id)
#
# Foreign Keys
#
#  fk_rails_...  (alert_id => alerts.id)
#
class AlertAction < ApplicationRecord
  belongs_to :alert
end
