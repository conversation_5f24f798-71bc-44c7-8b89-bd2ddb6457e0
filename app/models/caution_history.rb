# == Schema Information
#
# Table name: caution_histories
#
#  id                  :bigint           not null, primary key
#  description         :text
#  user_organization   :string
#  user_position_title :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  caution_id          :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_caution_histories_on_caution_id  (caution_id)
#  index_caution_histories_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (caution_id => cautions.id)
#  fk_rails_...  (user_id => users.id)
#
class CautionHistory < ApplicationRecord
  belongs_to :caution
  belongs_to :user

  validates :description, :user_organization, :user_position_title, presence: true


  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization if user_organization.present?
    titles.join(" - ")
  end
end
