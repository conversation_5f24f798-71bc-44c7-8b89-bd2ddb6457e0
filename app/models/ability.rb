# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    return unless user

    case user.role
    when 'lead_admin'
      lead_admin_abilities(user)
    when 'super_admin', 'restricted_admin'
      admin_abilities(user)
    when 'manager'
      manager_abilities(user)
    else
      user_abilities(user)
    end
  end

  private

  def lead_admin_abilities(user)
    admin_abilities(user)

    can :index, :admin_comment
    can :show, :admin_comment
    can :update, :admin_comment

    can :index, :service_crossover
    can :index, :group_crossover

    can :create, :notification_approval
    can :create, :notification_rejection
    can :create, Announcement
    can :read, Announcement
  end

  def admin_abilities(user)
    can :index, :service_crossover
    can :index, :group_crossover

    can :read, Announcement
    can :create, Service
    can :read, Service
    can :manage, ClientCohort
    can :create, Suggestion
    can :read, Suggestion
    can :edit, Suggestion
    can :index_all, Suggestion
    can :discard, Suggestion
    can :undiscard, Suggestion
    can :create, SuggestionHistory do |suggestion_history|
      suggestion_history.suggestion.editable?(user)
    end
    can :create, <PERSON><PERSON><PERSON>
    can :read, Caut<PERSON>
    can :edit, Caut<PERSON>
    can :index_all, <PERSON><PERSON><PERSON>
    can :discard, <PERSON><PERSON><PERSON>
    can :undiscard, <PERSON><PERSON><PERSON>
    can :create, CautionHistory do |caution_history|
      caution_history.caution.editable?(user)
    end
    can :manage, Alert
    can :manage, AlertLog
    can :manage, AlertHistory
    can :read, AdminSetting
    can :manage, Motel
    can :manage, User
    can :unlock, User
    can :discard, User do |other_user|
      other_user.id != user.id && User.where(
        role: ['restricted_admin', 'super_admin', 'lead_admin']
      ).where.not(id: user.id).exists?
    end
    can :restore, User do |other_user|
      other_user.id != user.id
    end

    can :read, Comment
    can :create, Comment
    can :archive, Comment
    can :pin, Comment

    can :read, CommentHistory
    can :create, CommentHistory do |comment_history|
      comment_history.comment.editable?(user)
    end
  end

  def manager_abilities(user)
    can :read, Announcement
    can :read, Service
    can :manage, ClientCohort
    can :create, Suggestion
    can :read, Suggestion
    can :edit, Suggestion
    can :index_all, Suggestion
    can :discard, Suggestion
    can :undiscard, Suggestion
    can :create, SuggestionHistory do |suggestion_history|
      suggestion_history.suggestion.editable?(user)
    end
    can :create, Caution
    can :read, Caution
    can :edit, Caution
    can :index_all, Caution
    can :discard, Caution
    can :undiscard, Caution
    can :create, CautionHistory do |caution_history|
      caution_history.caution.editable?(user)
    end
    can :manage, Alert
    can :manage, AlertLog
    can :manage, AlertHistory
    can :read, AdminSetting
    can :read, User
    can :primary_contacts, User
    can :manage, Motel
    can :read, Comment
    can :create, Comment
    can :pin, Comment
    can :discard, User, role: ['manager', 'user']  # Managers can discard other managers and standard users.
    cannot :discard, User, id: user.id  # But they can't discard themselves.
    cannot :restore, User  # Managers can't restore users.
    cannot :unlock, User  # Managers can't unlock users.
    can :read, CommentHistory
    can :create, CommentHistory do |comment_history|
      comment_history.comment.editable?(user)
    end
  end

  def user_abilities(user)
    can :read, Announcement
    can :read, Service
    can :read, ClientCohort
    can :read, Suggestion
    can :read, SuggestionHistory
    can :read, Caution
    can :read, CautionHistory
    can :read, Alert
    can :read, AlertHistory
    can :read, AlertLog
    can :update, Motel # Temporary while initial motel data is being created/updated.
    can :read, Motel
    can :read, Comment
    can :create, Comment
    can :read, CommentHistory
    can :create, CommentHistory do |comment_history|
      comment_history.comment.editable?(user)
    end
  end
end
