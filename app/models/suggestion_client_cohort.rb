# == Schema Information
#
# Table name: suggestion_client_cohorts
#
#  id               :bigint           not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  client_cohort_id :bigint           not null
#  suggestion_id    :bigint           not null
#
# Indexes
#
#  index_suggestion_client_cohorts_on_client_cohort_id  (client_cohort_id)
#  index_suggestion_client_cohorts_on_suggestion_id     (suggestion_id)
#
# Foreign Keys
#
#  fk_rails_...  (client_cohort_id => client_cohorts.id)
#  fk_rails_...  (suggestion_id => suggestions.id)
#
class SuggestionClientCohort < ApplicationRecord
  belongs_to :suggestion
  belongs_to :client_cohort
end
