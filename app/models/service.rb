# == Schema Information
#
# Table name: services
#
#  id                   :bigint           not null, primary key
#  name                 :string           not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  organization_type_id :bigint           not null
#
# Indexes
#
#  index_services_on_organization_type_id  (organization_type_id)
#
# Foreign Keys
#
#  fk_rails_...  (organization_type_id => organization_types.id)
#
class Service < ApplicationRecord
  belongs_to :organization_type
  has_many :motel_services
  has_many :motels, through: :motel_services
end
