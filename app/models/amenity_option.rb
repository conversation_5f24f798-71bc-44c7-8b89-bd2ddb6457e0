# == Schema Information
#
# Table name: amenity_options
#
#  id           :bigint           not null, primary key
#  color        :string           not null
#  display_text :string           not null
#  icon         :string           not null
#  name         :string           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  amenity_id   :bigint           not null
#
# Indexes
#
#  index_amenity_options_on_amenity_id  (amenity_id)
#
# Foreign Keys
#
#  fk_rails_...  (amenity_id => amenities.id)
#
class AmenityOption < ApplicationRecord
  belongs_to :amenity
  has_many :motel_amenities, dependent: :destroy

  validates_presence_of :name, :display_text, :icon, :color

  def is_unknown?
    name.downcase == 'unknown'
  end

  ### Transitional methods that support key-based as well as text-based attributes.
  # The majority of this logic can stay even after transitioning to enum-based options.

  def key
    self[:name].to_sym
  end

  def name
    return self[:name].capitalize if amenity.amenity_type == 'characteristic'

    self[:name]
  end

  def display_text
    I18n.t("#{i18n_key_prefix}.display_text", default: nil) ||
      I18n.t("#{i18n_default_key_prefix}.display_text", default: nil) ||
      self[:display_text]
  end

  def icon
    I18n.t("#{i18n_key_prefix}.icon", default: nil) ||
      I18n.t("#{i18n_default_key_prefix}.icon", default: nil) ||
      self[:icon]
  end

  def color
    if (value = I18n.t("#{i18n_key_prefix}.color", default: nil))
      return value
    end

    if self[:color] != self[:name]
      return self[:color]
    end

    case self[:name].to_sym
    when :yes
      'green'
    when :some
      'yellow'
    when :no
      'red'
    when :unknown
      'gray'
    end
  end

  def i18n_default_key_prefix
    "#{amenity.i18n_key_prefix}.options.default"
  end

  def i18n_key_prefix
    case (name = key)
    when :yes
      name = :true # For some reason, Rails's I18n changes `yes` to `true`
    when :no
      name = :false # For some reason, Rails's I18n changes `no` to `false`
    end
    "#{amenity.i18n_key_prefix}.options.#{name}"
  end

  ###
end
