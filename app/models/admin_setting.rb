# == Schema Information
#
# Table name: admin_settings
#
#  id                        :bigint           not null, primary key
#  reset_passcode            :string
#  reset_passcode_updated_at :datetime
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#
class AdminSetting < ApplicationRecord
  # Updates the password reset code and its updated timestamp.
  def update_reset_passcode
    self.reset_passcode = SecureRandom.alphanumeric(12)
    self.reset_passcode_updated_at = Time.current
  end
end
