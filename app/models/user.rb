# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  discarded_at           :datetime
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  first_name             :string
#  invitation_accepted_at :datetime
#  invitation_created_at  :datetime
#  invitation_limit       :integer
#  invitation_sent_at     :datetime
#  invitation_token       :string
#  invitations_count      :integer          default(0)
#  invited_by_type        :string
#  jti                    :string           not null
#  last_name              :string
#  locked_at              :datetime
#  password_changed_at    :datetime
#  primary_contact        :string
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("user"), not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  invited_by_id          :bigint
#
# Indexes
#
#  index_users_on_discarded_at          (discarded_at)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_invitation_token      (invitation_token) UNIQUE
#  index_users_on_invited_by            (invited_by_type,invited_by_id)
#  index_users_on_invited_by_id         (invited_by_id)
#  index_users_on_jti                   (jti) UNIQUE
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#
class User < ApplicationRecord
  include Devise::JWT::RevocationStrategies::JTIMatcher
  include Discard::Model

  has_many :comments
  has_many :comment_histories
  has_many :closed_alerts, class_name: 'Alert', foreign_key: 'closed_user_id'
  has_many :alert_logs
  has_many :alert_histories
  has_many :alerts
  has_many :cautions
  has_many :notifications
  has_many :notification_preferences

  devise :invitable, :database_authenticatable, :recoverable, :validatable, :lockable,
         :jwt_authenticatable, jwt_revocation_strategy: self

  enum role: { user: 0, manager: 1, restricted_admin: 2, super_admin: 3, lead_admin: 4 }

  scope :expired_invite, -> { where('invitation_sent_at IS NULL OR invitation_sent_at < ?', 2.days.ago) }

  before_save :update_password_changed_at, if: :will_save_change_to_encrypted_password?

  def password_needs_update?
    password_changed_at < 500.days.ago if password_changed_at.present?
  end

  def expired_invite?
    invitation_sent_at.nil? || invitation_sent_at < 2.days.ago
  end

  def new_user?
    invitation_accepted_at.present? && invitation_accepted_at >= 1.week.ago
  end

  def unaccepted_invite?
    invitation_sent_at.present? && invitation_accepted_at.blank?
  end

  def manager_or_admin?
    manager? || admin?
  end

  def admin?
    restricted_admin? || super_admin? || lead_admin?
  end

  def jwt_payload
    super.merge('role': role)
  end

  def full_name
    if first_name.nil? && last_name.nil?
      '-'
    elsif first_name.nil?
      last_name
    elsif last_name.nil?
      first_name
    else
      "#{first_name} #{last_name}"
    end
  end

  def user_full_name_initials
    full_name.split.map(&:first).join.upcase
  end

  private

  def update_password_changed_at
    self.password_changed_at = Time.current
  end
end
