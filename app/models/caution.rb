# == Schema Information
#
# Table name: cautions
#
#  id                  :bigint           not null, primary key
#  authorised_by       :string
#  description         :text
#  discarded_at        :datetime
#  user_organization   :string
#  user_position_title :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  motel_id            :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_cautions_on_motel_id  (motel_id)
#  index_cautions_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (user_id => users.id)
#
class Caution < ApplicationRecord
  belongs_to :motel
  belongs_to :user
  has_many :caution_histories, dependent: :destroy

  include Discard::Model

  validates :description, :authorised_by, :user_organization, :user_position_title, presence: true

  def archived?
    discarded_at.present?
  end

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization if user_organization.present?
    titles.join(" - ")
  end

  def editable?(user)
    if self.created_at >= 3.days.ago
      return true if self.user == user || user.admin? || user.manager?
    end
    false
  end

  def self.with_latest_timestamp
    self.select('cautions.*, GREATEST(cautions.created_at, COALESCE(MAX(caution_histories.created_at),
    cautions.created_at)) AS latest_timestamp')
      .left_joins(:caution_histories)
      .group('cautions.id')
      .order(latest_timestamp: :desc)
  end
end
