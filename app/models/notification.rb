# == Schema Information
#
# Table name: notifications
#
#  id                 :bigint           not null, primary key
#  approval_status    :integer
#  event_group_key    :uuid
#  is_read            :boolean
#  notification_event :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  alert_id           :bigint
#  announcement_id    :bigint
#  caution_id         :bigint
#  comment_id         :bigint
#  motel_id           :bigint
#  suggestion_id      :bigint
#  user_id            :bigint           not null
#
# Indexes
#
#  index_notifications_on_alert_id                     (alert_id)
#  index_notifications_on_announcement_id              (announcement_id)
#  index_notifications_on_caution_id                   (caution_id)
#  index_notifications_on_comment_id                   (comment_id)
#  index_notifications_on_event_group_key_and_user_id  (event_group_key,user_id) UNIQUE
#  index_notifications_on_motel_id                     (motel_id)
#  index_notifications_on_suggestion_id                (suggestion_id)
#  index_notifications_on_user_id                      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (alert_id => alerts.id)
#  fk_rails_...  (announcement_id => announcements.id)
#  fk_rails_...  (caution_id => cautions.id)
#  fk_rails_...  (comment_id => comments.id)
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (suggestion_id => suggestions.id)
#  fk_rails_...  (user_id => users.id)
#
class Notification < ApplicationRecord
  belongs_to :user

  belongs_to :motel, optional: true
  belongs_to :alert, optional: true
  belongs_to :caution, optional: true
  belongs_to :suggestion, optional: true
  belongs_to :comment, optional: true
  belongs_to :announcement, optional: true

  enum approval_status: {
    review_required: 0,
    approved: 1,
    rejected: 2,
    # A notification for the user who triggered the event, which is only for
    # record keeping purposes so we know not to generate a notification
    # again when later a lead admin approves the notification.
    self_triggered: 3
  }

  enum notification_event: {
    motel_created: 0,
    motel_updated: 1,
    motel_deactivated: 2,
    motel_reactivated: 3,

    alert_created: 100,
    alert_updated: 101,
    alert_discarded: 102,

    caution_created: 200,
    caution_updated: 201,
    caution_discarded: 202,
    caution_undiscarded: 203,

    suggestion_created: 300,
    suggestion_updated: 301,
    suggestion_discarded: 302,
    suggestion_undiscarded: 303,

    comment_created: 400,
    comment_updated: 401,
    comment_discarded: 402,
    comment_undiscarded: 403,
    comment_review_required: 404,

    user_created: 500, # Not yet used
    user_updated: 501, # Unused
    user_locked: 502, # Not yet used

    announcement_created: 600
  }

  validates :is_read, inclusion: [true, false]
  validates :notification_event, presence: true
  validates :approval_status, presence: true
  validates :event_group_key, presence: true, uniqueness: { scope: :user_id }

  validates :motel, presence: true, if: :motel_created?
  validates :motel, presence: true, if: :motel_updated?
  validates :motel, presence: true, if: :motel_deactivated?
  validates :motel, presence: true, if: :motel_reactivated?

  validates :alert, presence: true, if: :alert_created?
  validates :alert, presence: true, if: :alert_updated?
  validates :alert, presence: true, if: :alert_discarded?

  validates :caution, presence: true, if: :caution_created?
  validates :caution, presence: true, if: :caution_updated?
  validates :caution, presence: true, if: :caution_discarded?
  validates :caution, presence: true, if: :caution_undiscarded?

  validates :suggestion, presence: true, if: :suggestion_created?
  validates :suggestion, presence: true, if: :suggestion_updated?
  validates :suggestion, presence: true, if: :suggestion_discarded?
  validates :suggestion, presence: true, if: :suggestion_undiscarded?

  validates :comment, presence: true, if: :comment_review_required?
  validates :comment, presence: true, if: :comment_created?
  validates :comment, presence: true, if: :comment_updated?
  validates :comment, presence: true, if: :comment_discarded?
  validates :comment, presence: true, if: :comment_undiscarded?

  validates :announcement, presence: true, if: :announcement_created?

  scope :unread, -> { where(is_read: false) }
  scope :alert_notifications, -> { where(
    notification_event: [
      :alert_created,
      :alert_updated,
      :alert_discarded
    ]
  )}
  scope :caution_notifications, -> { where(
    notification_event: [
      :caution_created,
      :caution_updated,
      :caution_discarded,
      :caution_undiscarded
    ]
  )}
  scope :suggestion_notifications, -> { where(
    notification_event: [
      :suggestion_created,
      :suggestion_updated,
      :suggestion_discarded,
      :suggestion_undiscarded
    ]
  )}

  def target_motel
    if motel
      motel
    elsif alert
      alert.motel
    elsif caution
      caution.motel
    elsif suggestion
      suggestion.motel
    elsif comment
      comment.motel
    else
      nil
    end
  end

  def suggestion_for_red_cohort?
    suggestion&.red_cohort?
  end

  def self.mark_as_read(user, conditions)
    # Mark a notification as "read" when the user bypasses the notification page and opens
    # the relevant page directly.
    user.notifications.where(conditions).find_each do |notification|
      if !notification.update(is_read: true)
        Rollbar.error('Failed marking notification as read', notification.errors.full_messages.join(', '))
      end
    end
  end
end
