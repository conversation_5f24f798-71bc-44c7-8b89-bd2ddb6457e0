# == Schema Information
#
# Table name: alert_histories
#
#  id                  :bigint           not null, primary key
#  description         :string
#  user_organization   :string
#  user_position_title :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  alert_id            :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_alert_histories_on_alert_id  (alert_id)
#  index_alert_histories_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (alert_id => alerts.id)
#  fk_rails_...  (user_id => users.id)
#
class AlertHistory < ApplicationRecord
  belongs_to :alert
  belongs_to :user

  validates :description, presence: true
  validates :user_organization, presence: true
  validates :user_position_title, presence: true

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization if user_organization.present?
    titles.join(" - ")
  end
end
