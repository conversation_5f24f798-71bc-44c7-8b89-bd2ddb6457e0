# == Schema Information
#
# Table name: motel_change_logs
#
#  id                :bigint           not null, primary key
#  activity_type     :integer
#  amenity_note      :text
#  column_name       :string
#  column_value      :string
#  performed_at      :datetime
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  amenity_id        :bigint
#  amenity_option_id :bigint
#  motel_id          :bigint           not null
#  service_id        :bigint
#  user_id           :bigint           not null
#
# Indexes
#
#  index_motel_change_logs_on_amenity_id         (amenity_id)
#  index_motel_change_logs_on_amenity_option_id  (amenity_option_id)
#  index_motel_change_logs_on_motel_id           (motel_id)
#  index_motel_change_logs_on_service_id         (service_id)
#  index_motel_change_logs_on_user_id            (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (amenity_id => amenities.id)
#  fk_rails_...  (amenity_option_id => amenity_options.id)
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (service_id => services.id)
#  fk_rails_...  (user_id => users.id)
#

class MotelChangeLog < ApplicationRecord
  belongs_to :motel
  belongs_to :user

  belongs_to :amenity, optional: true
  belongs_to :amenity_option, optional: true
  belongs_to :service, optional: true

  enum activity_type: {
    add_motel_column: 0,
    update_motel_column: 1,
    remove_motel_column: 2,
    update_amenity: 100,
    add_amenity_note: 200,
    update_amenity_note: 201,
    remove_amenity_note: 202,
    add_service: 300,
    remove_service: 301,
  }

  validates :activity_type, presence: true
  validates :performed_at, presence: true

  validates :column_name, presence: true, if: -> { add_motel_column? || update_motel_column? || remove_motel_column? }
  validates :column_value, presence: true, if: -> { add_motel_column? || update_motel_column? }

  validates :amenity_id, presence: true, if: -> { add_amenity_note? || update_amenity_note? || remove_amenity_note? }
  validates :amenity_option_id, presence: true, if: :update_amenity?
  validates :amenity_note, presence: true, if: -> { add_amenity_note? || update_amenity_note? }

  validates :service_id, presence: true, if: :add_service?
  validates :service_id, presence: true, if: :remove_service?

  def save_with_performed_at!(performed_at)
    self.performed_at = performed_at
    save!
  end

  def self.group_and_sort_by_performed_at
    all.group_by(&:performed_at).transform_values do |logs|
      logs.sort_by do |log|
        case log.activity_type
        when 'add_motel_column' then 1
        when 'update_motel_column' then 2
        when 'remove_motel_column' then 3
        when 'update_amenity' then 4
        when 'add_amenity_note' then 5
        when 'update_amenity_note' then 6
        when 'remove_amenity_note' then 7
        when 'remove_service' then 8
        else 9
        end
      end
    end
  end
end
