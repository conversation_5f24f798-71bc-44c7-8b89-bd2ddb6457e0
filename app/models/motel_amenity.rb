# == Schema Information
#
# Table name: motel_amenities
#
#  id                :bigint           not null, primary key
#  note              :text
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  amenity_id        :bigint           not null
#  amenity_option_id :bigint           not null
#  motel_id          :bigint           not null
#
# Indexes
#
#  index_motel_amenities_on_amenity_id         (amenity_id)
#  index_motel_amenities_on_amenity_option_id  (amenity_option_id)
#  index_motel_amenities_on_motel_id           (motel_id)
#
# Foreign Keys
#
#  fk_rails_...  (amenity_id => amenities.id)
#  fk_rails_...  (amenity_option_id => amenity_options.id)
#  fk_rails_...  (motel_id => motels.id)
#
class MotelAmenity < ApplicationRecord
  belongs_to :motel
  belongs_to :amenity
  belongs_to :amenity_option

  validate :motel_presence, if: -> { motel_id.present? }

  def change_log(current_user)
    MotelChangeLog.new(
      activity_type: :update_amenity,
      user: current_user,
      motel_id: motel_id,
      amenity_id: amenity_id,
      amenity_option_id: amenity_option_id
    )
  end

  def note_change_log(current_user, type)
    MotelChangeLog.new(
      activity_type: "#{type}_amenity_note",
      user: current_user,
      motel_id: motel_id,
      amenity_id: amenity_id,
      amenity_option_id: amenity_option_id,
      amenity_note: note
    )
  end

  private

  def motel_presence
    errors.add(:motel, "can't be blank") if motel.blank?
  end
end
