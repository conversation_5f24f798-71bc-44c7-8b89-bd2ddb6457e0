# == Schema Information
#
# Table name: motel_services
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  motel_id   :bigint           not null
#  service_id :bigint           not null
#
# Indexes
#
#  index_motel_services_on_motel_id                 (motel_id)
#  index_motel_services_on_motel_id_and_service_id  (motel_id,service_id) UNIQUE
#  index_motel_services_on_service_id               (service_id)
#
# Foreign Keys
#
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (service_id => services.id)
#
class MotelService < ApplicationRecord
  belongs_to :motel
  belongs_to :service

  validates :service_id, uniqueness: { scope: :motel_id }

  def change_log(current_user, type)
    MotelChangeLog.new(
      activity_type: "#{type}_service",
      user: current_user,
      motel_id: motel_id,
      service_id: service_id,
    )
  end
end
