# == Schema Information
#
# Table name: motels
#
#  id         :bigint           not null, primary key
#  density    :string
#  duration   :string           default([]), is an Array
#  email      :string
#  inactive   :boolean
#  motel_type :string
#  name       :string
#  phone      :string
#  postcode   :integer
#  region     :string
#  state      :string           default("VIC")
#  street     :string
#  suburb     :string
#  website    :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#

class Motel < ApplicationRecord
  include PgSearch::Model

  pg_search_scope(
    :search_by_name,
    against: :name,
    using: {
      tsearch: { prefix: true }

    }
  )

  pg_search_scope(
    :search_by_suburb_or_postcode,
    against: [:suburb, :postcode],
    using: {
      tsearch: { prefix: true }
    }
)

  has_many :alerts, dependent: :destroy
  has_many :motel_amenities, dependent: :destroy
  has_many :amenities, through: :motel_amenities
  has_many :amenity_options, through: :motel_amenities
  has_many :comments
  has_many :cautions, dependent: :destroy
  has_many :suggestions, dependent: :destroy
  has_many :motel_services
  has_many :services, through: :motel_services
  has_many :organization_types, through: :services
  has_many :motel_change_logs

  accepts_nested_attributes_for :motel_amenities, allow_destroy: true
  accepts_nested_attributes_for :motel_services, allow_destroy: true

  validates :name, :street, :postcode, :suburb, :state, presence: true

  scope :with_alerts, -> { joins(:alerts).merge(Alert.not_archived).distinct }
  scope :with_cautions, -> { joins(:cautions).merge(Caution.kept).distinct }
  scope :with_suggestions, -> { joins(:suggestions).merge(Suggestion.kept).distinct }
  scope :where_inactive, -> { where(inactive: true) }

  DENSITIES = [
    "Low",
    "Medium",
    "High"
  ]

  validates :density, inclusion: { in: DENSITIES }, allow_blank: true

  DURATIONS = [
    'Single Night Stay',
    'Short Term Stay',
    'Long Term Stay'
  ]

  validate :duration_values_are_valid, unless: -> { duration.empty? }

  REGIONS = [
    "Barwon",
    "Bayside Peninsula",
    "Brimbank Melton",
    "Central Highlands",
    "Inner-Eastern Melbourne",
    "Outer-Eastern Melbourne",
    "Inner-Gippsland",
    "Outer-Gippsland",
    "Goulburn",
    "Hume Merri-Bek",
    "Loddon",
    "Mallee",
    "Melbourne CBD",
    "North-Eastern Melbourne",
    "Ovens Murray",
    "Southern Melbourne",
    "Outer Western District",
    "Western Melbourne"
  ]

  validates :region, inclusion: { in: REGIONS }, allow_blank: true

  MOTEL_TYPES = [
    "Hotel",
    "Apartment Hotel",
    "Caravan Park",
    "Homestay",
    "Hostel",
    "Motel/Motor Inn",
    "Rooming House",
    "Rooming House - Unregistered",
    "Serviced Apartments",
    "Single Unit",
    "SRS (Pension Level)",
    "SRS (Above Pension Level)"
  ]

  validates :motel_type, inclusion: { in: MOTEL_TYPES }, allow_blank: true

  def sorted_organization_types
    organization_types.distinct.sort_by do |org_type|
      name = org_type.name
      name.start_with?('Other') ? [1, name] : [0, name]
    end
  end

  def inactive?
    inactive.present?
  end

  def red_suggestion?
    suggestions.kept.any?(&:red_cohort?)
  end

  def change_logs(current_user)
    columns = [
      :name, :motel_type, :street, :suburb, :postcode, :region,
      :state, :phone, :email, :website, :duration, :density
    ]

    logs = []
    columns.each do |column|
      next unless send("#{column}_changed?")

      change_type = if send("#{column}_was").blank? && send(column).present?
        'add_motel_column'
      elsif send("#{column}_was").present? && send(column).blank?
        'remove_motel_column'
      else
        'update_motel_column'
      end

      column_value = if send(column).is_a?(Array)
        send(column).empty? ? nil : send(column).join(', ')
      else
        send(column)
      end

      logs << MotelChangeLog.new(
        activity_type: change_type,
        user: current_user,
        motel_id: id,
        column_name: column.to_s,
        column_value: column_value
      )
    end

    motel_amenities.each do |amenity|
      logs << amenity.change_log(current_user) if amenity.amenity_option_id_changed?

      next unless amenity.note_changed?

      change_type = if amenity.note_was.blank? && amenity.note.present?
        'add'
      elsif amenity.note_was.present? && amenity.note.blank?
        'remove'
      else
        'update'
      end

      logs << amenity.note_change_log(current_user, change_type)
    end

    logs
  end

  def duplicate!
    transaction do
      new_motel = dup
      new_motel.name = "#{name} - Copy #{DateTime.current.to_i}"
      new_motel.inactive = true

      motel_amenities.each do |motel_amenity|
        new_motel_amenity = motel_amenity.dup
        new_motel_amenity.motel = new_motel
        new_motel_amenity.save!
      end

      motel_services.each do |motel_service|
        new_motel_service = motel_service.dup
        new_motel_service.motel = new_motel
        new_motel_service.save!
      end

      new_motel.save!
      new_motel
    end
  end

  private

  def duration_values_are_valid
    if duration.any? { |value| !DURATIONS.include?(value) }
      errors.add(:duration, "contains invalid values")
    end
  end
end
