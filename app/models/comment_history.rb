# == Schema Information
#
# Table name: comment_histories
#
#  id                      :bigint           not null, primary key
#  body                    :text
#  user_organization_title :string           not null
#  user_position_title     :string           not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  comment_id              :bigint           not null
#  user_id                 :bigint           not null
#
# Indexes
#
#  index_comment_histories_on_comment_id  (comment_id)
#  index_comment_histories_on_user_id     (user_id)
#
class CommentHistory < ApplicationRecord
  belongs_to :comment
  belongs_to :user

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization_title if user_organization_title.present?
    titles.join(" - ")
  end
end
