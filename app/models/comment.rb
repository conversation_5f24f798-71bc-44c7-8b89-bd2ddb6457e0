# == Schema Information
#
# Table name: comments
#
#  id                      :bigint           not null, primary key
#  active_status           :integer
#  approved_at             :datetime
#  archived                :boolean          default(FALSE)
#  body                    :text
#  pinned                  :boolean          default(FALSE)
#  rejected_at             :datetime
#  rejection_note          :text
#  rejection_reason        :integer
#  user_organization_title :string           default(""), not null
#  user_position_title     :string           not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  motel_id                :bigint           not null
#  user_id                 :bigint           not null
#
# Indexes
#
#  index_comments_on_motel_id  (motel_id)
#  index_comments_on_user_id   (user_id)
#
class Comment < ApplicationRecord
  belongs_to :user
  belongs_to :motel
  has_many :comment_histories, dependent: :destroy

  enum active_status: {
    review_required: 0,
    approved: 1,
    rejected: 2,
  }

  enum rejection_reason: {
    request_submission_after_revision: 101,
    request_submission_as_an_alert: 102,
    request_submission_as_an_caution: 103,
    request_submission_as_a_suggestion: 104,
    tos_violation_personal_details_revealed: 201,
    tos_violation_inappropriate_language: 202,
    tos_violation_other: 203
  }

  validates :body, presence: true
  validates :active_status, presence: true
  validates :user_position_title, presence: true
  validates :user_organization_title, presence: true

  validates :approved_at, presence: true, if: :approved?
  validates :rejected_at, presence: true, if: :rejected?
  validates :rejection_reason, presence: true, if: :rejected?
  validates :rejection_note, presence: true, if: :rejected?

  validates :rejection_reason, absence: true, unless: :rejected?
  validates :rejection_note, absence: true, unless: :rejected?

  scope :not_archived, -> { where(archived: false) }

  def editable?(user)
    return false unless approved?

    if self.created_at >= 3.days.ago
      return true if user.admin? || user.manager? || self.user == user
    end
    false
  end

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization_title if user_organization_title.present?
    titles.join(" - ")
  end

  def rejection_reason_text
    self.class.rejection_reason_text(rejection_reason) if rejection_reason
  end

  def self.with_latest_timestamp_for(motel)
    self.select('comments.*, GREATEST(comments.created_at, COALESCE(MAX(comment_histories.created_at),
    comments.created_at)) AS latest_timestamp')
      .where(motel: motel)
      .left_joins(:comment_histories)
      .group('comments.id')
      .order(pinned: :desc, latest_timestamp: :desc)
  end

  def self.rejection_reason_text(rejection_reason)
    I18n.t("comments.rejection_reason.#{rejection_reason}")
  end
end
