# == Schema Information
#
# Table name: alert_logs
#
#  id                  :bigint           not null, primary key
#  log_type            :string
#  user_organization   :string
#  user_position_title :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  alert_id            :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_alert_logs_on_alert_id  (alert_id)
#  index_alert_logs_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (alert_id => alerts.id)
#  fk_rails_...  (user_id => users.id)
#
class AlertLog < ApplicationRecord
  belongs_to :alert
  belongs_to :user

  validates :log_type, presence: true
  validates :user_organization, presence: true
  validates :user_position_title, presence: true


  LOG_TYPES = [
    'created',
    'updated',
    'closed',
    'action_added',
  ]

  validates :log_type, presence: true, inclusion: { in: LOG_TYPES }

  def self.log_types
    LOG_TYPES
  end

  def log_description
    case log_type
    when 'created'
      "An alert has been created for #{alert.motel.name}"
    when 'updated'
      "An alert has been updated for #{alert.motel.name}"
    when 'closed'
      "An alert has been closed for #{alert.motel.name}"
    when 'action_added'
      "A new action has been added to an alert for #{alert.motel.name}"
    end
  end

  def user_full_name
    user.full_name
  end

  def user_position_organization
    titles = []
    titles << user_position_title if user_position_title.present?
    titles << user_organization if user_organization.present?
    titles.join(" - ")
  end
end
