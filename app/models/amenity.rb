# == Schema Information
#
# Table name: amenities
#
#  id           :bigint           not null, primary key
#  amenity_type :string           not null
#  help_text    :text
#  name         :string           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#

class Amenity < ApplicationRecord
  has_many :amenity_options, dependent: :destroy
  has_many :motel_amenities, dependent: :destroy

  validates_presence_of :amenity_type, :key

  def ordered_amenity_options
    color_order = ['green', 'yellow', 'red', 'gray']
    case_sql = color_order.map.with_index { |color, i| "WHEN '#{color}' THEN #{i}" }.join("\n")
    case_sql += "\nELSE #{color_order.length}"
    amenity_options.order(Arel.sql("CASE color #{case_sql} END"))
  end

  def automatically_updated?
    I18n.t("#{i18n_key_prefix}.automatically_updated", default: false) == true
  end

  ### Transitional methods that support key-based as well as text-based attributes.

  def name
    I18n.t("#{i18n_key_prefix}.name", default: self[:name])
  end

  def help_text
    I18n.t("#{i18n_key_prefix}.help_text", default: self[:help_text])
  end

  def key
    self[:name].to_sym
  end

  def i18n_key_prefix
    "amenities.#{key}"
  end

  ###
end
