class NotificationConfig
  def recipients
    raise 'Implementation needed'
  end

  def send_emails?
    true
  end

  def default_email_recipient_roles
    [:lead_admin, :user]
  end

  def review_required?
    false
  end

  # Set this to `false` for:
  # 1) Notifications that should never require review even when it's triggered by a
  #    restricted_admin, or
  # 2) Notifications that should always require review even when it's triggered by a
  #    lead admin.
  def check_user_role?
    true
  end

  def self.admin_user_roles
    [:lead_admin, :super_admin, :restricted_admin]
  end

  def self.manager_user_roles
    self.admin_user_roles + [:manager]
  end

  def self.all_user_roles
    self.manager_user_roles + [:user]
  end
end
