class CreateNotificationJob < ApplicationJob
  def perform(
    notification_event,
    current_user_id,
    motel: nil,
    alert: nil,
    caution: nil,
    suggestion: nil,
    comment: nil,
    announcement: nil,
    event_group_key: nil)

    if event_group_key
      notification_approved = true
    else
      notification_approved = false

      # A key that unifies all the notifications generated from the same event.
      event_group_key = SecureRandom.uuid
    end

    notification_config = "#{notification_event}_notification_config".camelize.constantize.new
    recipients(notification_config, notification_approved, current_user_id).find_each do |recipient_user|
      # Only create a notification if it hasn't been previously created, e.g. a lead admin would already
      # have a `review_required` notification for certain events
      #
      # Note that it is important that `approval_status` will not be updated if the notification already exists,
      # because we don't want to send a notification to the person who originally triggered the `review_required`
      # notifcation.
      notification = Notification.where(user: recipient_user, event_group_key: event_group_key).first_or_create!(
        notification_event: notification_event,
        is_read: false,
        motel: motel,
        alert: alert,
        caution: caution,
        suggestion: suggestion,
        comment: comment,
        announcement: announcement,
        approval_status: approval_status(notification_config, notification_approved, current_user_id, recipient_user)
      )

      newly_created = notification.id_previously_changed?
      rejected = notification.self_triggered?

      if newly_created && !rejected && notification_config.send_emails?
        NotificationMailer.with(notification: notification).created.deliver_later
      end
    end
  end

  def approval_status(notification_config, notification_approved, current_user_id, recipient_user)
    if notification_config.check_user_role?
      # See Notification#approval_status
      return :self_triggered if recipient_user.id == current_user_id
    end

    review_required?(notification_config, notification_approved, current_user_id) ? :review_required : :approved
  end

  def recipients(notification_config, notification_approved, current_user_id)
    # Notify lead admins first if a non-preapproved activity was performed by anyone other than a lead admin.
    if review_required?(notification_config, notification_approved, current_user_id)
      return User.where(id: User.lead_admin.pluck(:id) + [current_user_id])
    end

    notification_config.recipients
  end

  def review_required?(notification_config, notification_approved, current_user_id)
    current_user = User.find(current_user_id)

    if notification_config.check_user_role?
      return true if current_user.restricted_admin?
      return false if current_user.lead_admin?
    end

    !notification_approved && notification_config.review_required?
  end
end
