module ApplicationHelper
  def format_date(datetime)
    datetime.in_time_zone.strftime('%d/%m/%y')
  end

  def format_time(datetime)
    datetime.in_time_zone.strftime('%-I:%M%p')
  end

  def format_date_time(datetime)
    datetime.in_time_zone.strftime('%-I:%M %p %d/%m/%y')
  end

  def compact_time_ago(from_time, to_time = Time.current)
    diff_in_minutes = ((to_time - from_time) / 60).round
    case diff_in_minutes
    when 0..1       then 'Just Now'
    when 2..59      then "#{diff_in_minutes}m"
    when 60..1439   then "#{diff_in_minutes / 60}h"
    when 1440..10079 then "#{diff_in_minutes / 1440}d"
    ## May uncomment this if we add additional insights/reporting in the admin dashboard
    # when 10080..40319 then "#{diff_in_minutes / 10080}w"
    # when 40320..525599 then "#{diff_in_minutes / 40320}mo"
    # else "#{diff_in_minutes / 525600}y"
    else
      from_time.strftime('%d/%m/%y')
    end
  end
end
