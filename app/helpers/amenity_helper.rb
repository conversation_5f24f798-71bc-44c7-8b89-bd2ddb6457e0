module AmenityHelper
  def amenity_option_background_color(amenity_option)
    case amenity_option.color
    when 'green'
      '#72e776'
    when 'red'
      '#ff5e5e'
    when 'yellow'
      '#ffee54'
    when 'gray'
      '#e0e0e0'
    when 'blue'
      '#e1f5fe'
    when 'danger'
      '#991b1a'
    when 'danger_secondary'
      '#fff'
    else
      '#000000'
    end
  end

  def amenity_option_text_color(amenity_option)
    case amenity_option.color
    when 'blue'
      '#05a'
    when 'danger_secondary'
      '#991b1a'
    when 'danger'
      '#ffffff'
    else
      '#000000'
    end
  end

  def amenity_option_border_color(amenity_option)
    case amenity_option.color
    when 'danger_secondary'
      '#991b1a'
    else
      nil
    end
  end
end
