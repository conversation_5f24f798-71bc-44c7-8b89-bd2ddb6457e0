module MotelChangeLog<PERSON>el<PERSON>
  def humanize_column_name(column_name)
    return unless column_name

    case column_name
    when 'name'
      'accommodation name'
    when 'motel_type'
      'accommodation type'
    when 'website'
      'website URL'
    else
      column_name
    end
  end

  def activity_type_action(activity_type)
    return unless activity_type

    action = activity_type.split('_').first

    case action
    when 'add'
      {
        text: 'Added ',
        color: '#4caf50'
      }
    when 'update'
      {
        text: 'Updated ',
        color: '#0091ea'
      }
    when 'remove'
      {
        text: 'Removed ',
        color: '#d50000'
      }
    end
  end

  def change_log_value(change_log)
    return unless change_log

    case change_log.activity_type
    when 'add_motel_column', 'update_motel_column'
      change_log.column_value
    when 'add_service', 'remove_service'
      change_log.service&.name
    when 'add_amenity_note', 'update_amenity_note'
      change_log.amenity_note
    when 'update_amenity'
      value = change_log.amenity_option&.display_text
      value = "#{value} (Unknown)" if change_log.amenity_option&.is_unknown?
      value
    else
      nil
    end
  end

  def change_log_message(change_log)
    return unless change_log

    case change_log.activity_type
    when 'add_motel_column', 'update_motel_column'
      "#{humanize_column_name(change_log.column_name)}: "
    when 'remove_motel_column'
      humanize_column_name(change_log.column_name)
    when 'add_service', 'remove_service'
      'service provider: '
    when 'add_amenity_note'
      "note to \"#{change_log.amenity_option&.display_text}\": "
    when 'update_amenity_note'
      "note for \"#{change_log.amenity_option&.display_text}\": "
    when 'remove_amenity_note'
      "note from \"#{change_log.amenity_option&.display_text}\""
    when 'update_amenity'
      "amenity: "
    end
  end
end
