module AuditHelper
  def audit_organization_type_colors(organization_type)
    case organization_type.name
    when 'Access Point/IAP'
      backgroundColor = '#01579B'
      dividerColor = '#253c4f'
      color = '#fff'
    when 'Local FV Support Service'
      backgroundColor = '#00695C'
      dividerColor = '#053f38'
      color = '#fff'
    when 'PUV/Justice Service'
      backgroundColor = '#991b1a'
      dividerColor = '#991b1a'
      color = '#fff'
    when 'Safe Steps'
      backgroundColor = '#7055c5'
      dividerColor = '#4f35a2'
      color = '#fff'
    when 'The Orange Door'
      backgroundColor = '#e57200'
      dividerColor = '#cb6b0c'
      color = '#fff'
    when 'Other (Books EA for Females/Victim-Survivors)'
      backgroundColor = '#e0dbfd'
      dividerColor = '#b6a9e9'
      color = '#7055c5'
    when 'Other (Books EA for Males/Perpetrators)'
      backgroundColor = '#fee6ec'
      dividerColor = '#dfa9ad'
      color = '#991b1a'
    when 'Salvation Army St Kilda Crisis Centre'
      backgroundColor = '#e0e0e0'
      dividerColor = '#c3c3c3'
      color = '#000000'
    else
      backgroundColor = '#e1f5fe'
      dividerColor = '#bddeff'
      color = '#05a'
    end

    return backgroundColor, dividerColor, color
  end

  def audit_amenity_colors(amenity)
    case amenity.key
    when :registered_sex_offenders
      backgroundColor = '#ff5e5e'
      dividerColor = '#bddeff'
      color = '#000000'
    when :persons_using_violence
      backgroundColor = '#991b1a'
      dividerColor = '#bddeff'
      color = '#fff'
    else
      backgroundColor = '#e1f5fe'
      dividerColor = '#bddeff'
      color = '#05a'
    end

    return backgroundColor, dividerColor, color
  end
end
