module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def encrypt_email_unsubscription(data)
    encryption_secret = Rails.application.credentials.dig(:email_unsubscription, :encryption_secret)
    encryption_salt = Rails.application.credentials.dig(:email_unsubscription, :encryption_salt)
    encrypt(data, encryption_secret, encryption_salt)
  end

  def decrypt_email_unsubscription(encrypted_data)
    encryption_secret = Rails.application.credentials.dig(:email_unsubscription, :encryption_secret)
    encryption_salt = Rails.application.credentials.dig(:email_unsubscription, :encryption_salt)
    decrypt(encrypted_data, encryption_secret, encryption_salt)
  end

  def encrypt(original_message, encryption_secret, encryption_salt)
    encryptor = message_encryptor(encryption_secret, encryption_salt)
    CGI.escape(encryptor.encrypt_and_sign(original_message))
  end

  def decrypt(encrypted_message, encryption_secret, encryption_salt)
    encryptor = message_encryptor(encryption_secret, encryption_salt)
    begin
      encryptor.decrypt_and_verify(CGI.unescape(encrypted_message))
    rescue ActiveSupport::MessageEncryptor::InvalidMessage
      nil
    end
  end

  private

  def message_encryptor(encryption_secret, encryption_salt)
    key_generator = ActiveSupport::KeyGenerator.new(encryption_secret, iterations: 1000)
    key_secret = key_generator.generate_key(encryption_salt, 32)
    ActiveSupport::MessageEncryptor.new(key_secret, digest: 'SHA256', serializer: JSON)
  end
end
