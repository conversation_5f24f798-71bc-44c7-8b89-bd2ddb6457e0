class FilterMotelsQuery
  def initialize(filter_params: {}, page:, tab:)
    @filters = filter_params
    @show_unknown_amenities = @filters['show_unknown_amenities'] || false
    @page = (page.presence || 1).to_i
    @tab = tab
  end

  def call
    query = apply_search_filters(Motel.all)

    counts = calculate_counts(query)

    query = apply_tab_filter(query)
    motels = query.page(@page).per(40)

    [ motels, counts ]
  end

  private

  attr_reader :filters, :show_unknown_amenities, :tab, :page

  def apply_search_filters(query)
    query = query.select('motels.*, LOWER(motels.name) as ordered_name')
    query = apply_name_filter(query)
    query = apply_suburb_or_postcode_filter(query)
    query = apply_services_filter(query)
    query = apply_organization_type_filter(query)
    query = apply_exclude_organization_type_filter(query)
    query = apply_regions_filter(query)
    query = apply_exclude_regions_filter(query)
    query = apply_motel_types_filter(query)
    query = apply_densities_filter(query)
    query = apply_durations_filter(query)
    query = apply_motel_characteristics_filter(query)
    query = apply_safety_requirements_filter(query)
    query = apply_main_amenities_filter(query)
    query = apply_additional_amenities_filter(query)

    query.reorder("ordered_name ASC").distinct
  end

  def calculate_counts(query)
    return {} if page > 1

    {
      all: query.count(:id),
      suggestion: query.with_suggestions.count(:id),
      alert: query.with_alerts.count(:id),
      caution: query.with_cautions.count(:id),
      inactive: query.where_inactive.count(:id)
    }
  end

  def apply_tab_filter(query)
    case tab
    when 'suggestion'
      query.with_suggestions
    when 'alert'
      query.with_alerts
    when 'caution'
      query.with_cautions
    when 'inactive'
      query.where_inactive
    else
      query
    end
  end

  def apply_name_filter(query)
    return query if filters['name'].blank?

    query.search_by_name(filters['name'])
  end

  def apply_suburb_or_postcode_filter(query)
    return query if filters['suburb_or_postcode'].blank?

    query.search_by_suburb_or_postcode(filters['suburb_or_postcode'])
  end

  def apply_services_filter(query)
    return query if filters['services'].blank?

    services_ids = Array(filters['services'])

    services_motel_ids = Motel.joins(:motel_services)
                    .where(motel_services: { service_id: services_ids })
                    .distinct
                    .select(:id)

    query.where(id: services_motel_ids)
  end

  def apply_organization_type_filter(query)
    return query if filters['organization_types'].blank?

    organization_type_ids = Array(filters['organization_types'])

    organization_type_motel_ids = Motel.joins(services: :organization_type)
                                       .where(organization_types: { id: organization_type_ids })
                                       .group('motels.id')

    query.where(id: organization_type_motel_ids)
  end

  def apply_exclude_organization_type_filter(query)
    return query if filters['exclude_organization_types'].blank?

    exclude_organization_type_ids = Array(filters['exclude_organization_types'])

    excluded_motels = Motel.joins(services: :organization_type)
                           .where(organization_types: { id: exclude_organization_type_ids })
                           .group('motels.id')
                           .pluck(:id)

    query.where.not(id: excluded_motels)
  end

  def apply_regions_filter(query)
    return query if filters['regions'].blank?

    regions = Array(filters['regions'])

    query.where(region: regions)
  end

  def apply_exclude_regions_filter(query)
    return query if filters['exclude_regions'].blank?

    exclude_regions = Array(filters['exclude_regions'])

    query.where.not(region: exclude_regions)
  end

  def apply_motel_types_filter(query)
    return query if filters['motel_types'].blank?

    motel_types = Array(filters['motel_types'])

    query.where(motel_type: motel_types)
  end

  def apply_densities_filter(query)
    return query if filters['densities'].blank?

    densities = Array(filters['densities'])

    query.where(density: densities)
  end

  def apply_durations_filter(query)
    return query if filters['durations'].blank?

    durations = Array(filters['durations'])

    query.where("duration && ARRAY[?]::varchar[]", durations)
  end

  # TODO: Refactor below when amenity/amenity_options is refactored

  def apply_motel_characteristics_filter(query)
    return query if filters['motel_characteristics'].blank?

    motel_ids_satisfying_all_motel_characteristics = []

    motel_characteristic_ids = filters['motel_characteristics']
    motel_characteristic_ids.each_with_index do  |motel_characteristic_id, index|
      amenity = Amenity.joins(:amenity_options).find_by(amenity_options: { id: motel_characteristic_id })

      if show_unknown_amenities
        included_keys = [:yes, :unknown]
      else
        included_keys = [:yes]
      end
      amenity_option_ids = amenity.amenity_options.to_a.map do |amenity_option|
        included_keys.include?(amenity_option.key) ? amenity_option.id : nil
      end.compact

      motel_characteristic_motel_ids = Motel.joins(:motel_amenities)
                                            .where(motel_amenities: { amenity_option_id: amenity_option_ids })
                                            .pluck("motels.id")

      if index.zero?
        motel_ids_satisfying_all_motel_characteristics = motel_characteristic_motel_ids
      else
        motel_ids_satisfying_all_motel_characteristics &= motel_characteristic_motel_ids
      end
    end

    query.where(id: motel_ids_satisfying_all_motel_characteristics)
  end

  def apply_safety_requirements_filter(query)
    return query if filters['safety_requirements'].blank?

    motel_ids_satisfying_all_safety_requirements = []

    booking_green_id = AmenityOption.find_by(display_text: '24h Booking', color: 'green')&.id
    booking_yellow_id = AmenityOption.find_by(display_text: 'Out-of-Hours Booking', color: 'yellow')&.id
    booking_gray_id = AmenityOption.find_by(display_text: 'Out-of-Hours Booking', color: 'gray')&.id
    manager_green_id = AmenityOption.find_by(display_text: 'Manager Available 24h', color: 'green')&.id
    manager_yellow_id = AmenityOption.find_by(display_text: 'Manager Available After Hours', color: 'yellow')&.id
    manager_gray_id = AmenityOption.find_by(display_text: 'Manager Available After Hours', color: 'gray')&.id
    staffed_reception_green_id = AmenityOption.find_by(display_text: '24h Staffed Reception', color: 'green')&.id
    staffed_reception_yellow_id = AmenityOption.find_by(display_text: 'Staffed Reception', color: 'yellow')&.id
    staffed_reception_gray_id = AmenityOption.find_by(display_text: 'Staffed Reception', color: 'gray')&.id

    special_amenity_ids = [
      booking_green_id, booking_yellow_id, manager_green_id, manager_yellow_id,
      staffed_reception_green_id, staffed_reception_yellow_id
    ]

    safety_requirement_ids = filters['safety_requirements'].reject do |safety_requirement_id|
      special_amenity_ids.include?(safety_requirement_id)
    end

    safety_requirement_ids.each_with_index do  |safety_requirement, index|
      amenity = Amenity.joins(:amenity_options).find_by(amenity_options: { id: safety_requirement })

      if show_unknown_amenities
        amenity_ids = amenity.amenity_options.where(color: ['green', 'gray']).pluck(:id)
      else
        amenity_ids = amenity.amenity_options.where(color: 'green').pluck(:id)
      end

      safety_requirement_motel_ids = Motel.joins(:motel_amenities)
                                          .where(motel_amenities: { amenity_option_id: amenity_ids })
                                          .pluck("motels.id")

      if index.zero?
        motel_ids_satisfying_all_safety_requirements = safety_requirement_motel_ids
      else
        motel_ids_satisfying_all_safety_requirements &= safety_requirement_motel_ids
      end
    end

    special_cases = [
      {
        green: booking_green_id,
        yellow: booking_yellow_id,
        gray: booking_gray_id
      },
      {
        green: manager_green_id,
        yellow: manager_yellow_id,
        gray: manager_gray_id
      },
      {
        green: staffed_reception_green_id,
        yellow: staffed_reception_yellow_id,
        gray: staffed_reception_gray_id
      }
    ]

    motel_ids_satisfying_all_safety_requirements = Motel.pluck(:id) if motel_ids_satisfying_all_safety_requirements.empty?

    special_cases.each do |special_case|
      # Skip this special_case if neither green nor yellow is in filters['safety_requirements']
      next unless filters['safety_requirements'].include?(special_case[:green]) || filters['safety_requirements'].include?(special_case[:yellow])

      green_ids = []
      yellow_ids = []
      gray_ids = []

      # if filters['show_unknown_amenity_options'].present? && filters['show_unknown_amenity_options'] == 'false'

      # If green is in filters['safety_requirements'], include green_ids and gray_ids
      if filters['safety_requirements'].include?(special_case[:green])
        green_ids = [special_case[:green]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      # If yellow is in filters['safety_requirements'], include yellow_ids, green_ids, and gray_ids
      if filters['safety_requirements'].include?(special_case[:yellow])
        green_ids = [special_case[:green]]
        yellow_ids = [special_case[:yellow]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      motel_ids = Motel.joins(:motel_amenities)
                       .where(motel_amenities: { amenity_option_id: green_ids + yellow_ids + gray_ids })
                       .pluck("motels.id")

      motel_ids_satisfying_all_safety_requirements &= motel_ids
    end

    query.where(id: motel_ids_satisfying_all_safety_requirements)
  end

  def apply_main_amenities_filter(query)
    return query if filters['main_amenities'].blank?

    motel_ids_satisfying_all_main_amenities = []

    kitchen_green_id = AmenityOption.find_by(display_text: 'Kitchen (Private)', color: 'green')&.id
    kitchen_yellow_id = AmenityOption.find_by(display_text: 'Kitchenette', color: 'yellow')&.id
    kitchen_gray_id = AmenityOption.find_by(display_text: 'Kitchen', color: 'gray')&.id
    smoking_area_green_id = AmenityOption.find_by(display_text: 'Private Smoking Area', color: 'green')&.id
    smoking_area_yellow_id = AmenityOption.find_by(display_text: 'Communal Smoking Area', color: 'yellow')&.id
    smoking_area_gray_id = AmenityOption.find_by(display_text: 'Smoking Area', color: 'gray')&.id
    laundry_green_id = AmenityOption.find_by(display_text: 'Laundry (Free)', color: 'green')&.id
    laundry_yellow_id = AmenityOption.find_by(display_text: 'Laundry (Paid)', color: 'yellow')&.id
    laundry_gray_id = AmenityOption.find_by(display_text: 'Laundry', color: 'gray')&.id

    special_amenity_ids = [
      kitchen_green_id, kitchen_yellow_id, smoking_area_green_id, smoking_area_yellow_id,
      laundry_green_id, laundry_yellow_id
    ]

    main_amenity_ids = filters['main_amenities'].reject do |main_amenity_id|
      special_amenity_ids.include?(main_amenity_id)
    end

    main_amenity_ids.each_with_index do  |main_amenity, index|
      amenity = Amenity.joins(:amenity_options).find_by(amenity_options: { id: main_amenity })

      if show_unknown_amenities
        amenity_ids = amenity.amenity_options.where(color: ['green', 'gray']).pluck(:id)
      else
        amenity_ids = amenity.amenity_options.where(color: 'green').pluck(:id)
      end

      main_amenity_motel_ids = Motel.joins(:motel_amenities)
                                          .where(motel_amenities: { amenity_option_id: amenity_ids })
                                          .pluck("motels.id")

      if index.zero?
        motel_ids_satisfying_all_main_amenities = main_amenity_motel_ids
      else
        motel_ids_satisfying_all_main_amenities &= main_amenity_motel_ids
      end
    end

    special_cases = [
      {
        green: kitchen_green_id,
        yellow: kitchen_yellow_id,
        gray: kitchen_gray_id
      },
      {
        green: smoking_area_green_id,
        yellow: smoking_area_yellow_id,
        gray: smoking_area_gray_id
      },
      {
        green: laundry_green_id,
        yellow: laundry_yellow_id,
        gray: laundry_gray_id
      }
    ]

    motel_ids_satisfying_all_main_amenities = Motel.pluck(:id) if motel_ids_satisfying_all_main_amenities.empty?

    special_cases.each do |special_case|
      # Skip this special_case if neither green nor yellow is in filters['main_amenities']
      next unless filters['main_amenities'].include?(special_case[:green]) || filters['main_amenities'].include?(special_case[:yellow])

      green_ids = []
      yellow_ids = []
      gray_ids = []

      # if filters['show_unknown_amenity_options'].present? && filters['show_unknown_amenity_options'] == 'false'

      # If green is in filters['main_amenities'], include green_ids and gray_ids
      if filters['main_amenities'].include?(special_case[:green])
        green_ids = [special_case[:green]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      # If yellow is in filters['main_amenities'], include yellow_ids, green_ids, and gray_ids
      if filters['main_amenities'].include?(special_case[:yellow])
        green_ids = [special_case[:green]]
        yellow_ids = [special_case[:yellow]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      motel_ids = Motel.joins(:motel_amenities)
                       .where(motel_amenities: { amenity_option_id: green_ids + yellow_ids + gray_ids })
                       .pluck("motels.id")

      motel_ids_satisfying_all_main_amenities &= motel_ids
    end

    query.where(id: motel_ids_satisfying_all_main_amenities)
  end

  def apply_additional_amenities_filter(query)
    return query if filters['additional_amenities'].blank?

    motel_ids_satisfying_all_additional_amenities = []

    parking_green_id = AmenityOption.find_by(display_text: 'Free On-Site Parking', color: 'green')&.id
    parking_yellow_id = AmenityOption.find_by(display_text: 'Paid On-Site Parking', color: 'yellow')&.id
    parking_gray_id = AmenityOption.find_by(display_text: 'On-Site Parking', color: 'gray')&.id
    pets_allowed_green_id = AmenityOption.find_by(display_text: 'All Pets Allowed', color: 'green')&.id
    pets_allowed_yellow_id = AmenityOption.find_by(display_text: 'Some Pets Allowed', color: 'yellow')&.id
    pets_allowed_gray_id = AmenityOption.find_by(display_text: 'Pets Allowed', color: 'gray')&.id
    wifi_internet_green_id = AmenityOption.find_by(display_text: 'Free Wifi/Internet', color: 'green')&.id
    wifi_internet_yellow_id = AmenityOption.find_by(display_text: 'Wifi/Internet', color: 'yellow')&.id
    wifi_internet_gray_id = AmenityOption.find_by(display_text: 'Wifi/Internet', color: 'gray')&.id

    special_amenity_ids = [
      parking_green_id, parking_yellow_id, pets_allowed_green_id, pets_allowed_yellow_id,
      wifi_internet_green_id, wifi_internet_yellow_id
    ]

    additional_amenity_ids = filters['additional_amenities'].reject do |additional_amenity_id|
      special_amenity_ids.include?(additional_amenity_id)
    end

    additional_amenity_ids.each_with_index do  |additional_amenity, index|
      amenity = Amenity.joins(:amenity_options).find_by(amenity_options: { id: additional_amenity })

      if show_unknown_amenities
        amenity_ids = amenity.amenity_options.where(color: ['green', 'gray']).pluck(:id)
      else
        amenity_ids = amenity.amenity_options.where(color: 'green').pluck(:id)
      end

      additional_amenity_motel_ids = Motel.joins(:motel_amenities)
                                          .where(motel_amenities: { amenity_option_id: amenity_ids })
                                          .pluck("motels.id")

      if index.zero?
        motel_ids_satisfying_all_additional_amenities = additional_amenity_motel_ids
      else
        motel_ids_satisfying_all_additional_amenities &= additional_amenity_motel_ids
      end
    end

    special_cases = [
      {
        green: parking_green_id,
        yellow: parking_yellow_id,
        gray: parking_gray_id
      },
      {
        green: pets_allowed_green_id,
        yellow: pets_allowed_yellow_id,
        gray: pets_allowed_gray_id
      },
      {
        green: wifi_internet_green_id,
        yellow: wifi_internet_yellow_id,
        gray: wifi_internet_gray_id
      }
    ]

    motel_ids_satisfying_all_additional_amenities = Motel.pluck(:id) if motel_ids_satisfying_all_additional_amenities.empty?

    special_cases.each do |special_case|
      # Skip this special_case if neither green nor yellow is in filters['additional_amenities']
      next unless filters['additional_amenities'].include?(special_case[:green]) || filters['additional_amenities'].include?(special_case[:yellow])

      green_ids = []
      yellow_ids = []
      gray_ids = []

      # if filters['show_unknown_amenity_options'].present? && filters['show_unknown_amenity_options'] == 'false'

      # If green is in filters['additional_amenities'], include green_ids and gray_ids
      if filters['additional_amenities'].include?(special_case[:green])
        green_ids = [special_case[:green]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      # If yellow is in filters['additional_amenities'], include yellow_ids, green_ids, and gray_ids
      if filters['additional_amenities'].include?(special_case[:yellow])
        green_ids = [special_case[:green]]
        yellow_ids = [special_case[:yellow]]
        gray_ids = [special_case[:gray]] if show_unknown_amenities
      end

      motel_ids = Motel.joins(:motel_amenities)
                       .where(motel_amenities: { amenity_option_id: green_ids + yellow_ids + gray_ids })
                       .pluck("motels.id")

      motel_ids_satisfying_all_additional_amenities &= motel_ids
    end

    query.where(id: motel_ids_satisfying_all_additional_amenities)
  end
  # TODO: Refactor below when amenity/amenity_options is refactored
  ##############################################
  # Apply filters for different amenity categories
  # def apply_amenity_filters(query)
  # query = apply_amenity_filter(query, 'safety_requirements', AMENITY_OPTIONS[:safety])
  # query = apply_amenity_filter(query, 'main_amenities', AMENITY_OPTIONS[:main])
  # query = apply_amenity_filter(query, 'additional_amenities', AMENITY_OPTIONS[:additional])
  # query
  # end
  ##############################################
end
