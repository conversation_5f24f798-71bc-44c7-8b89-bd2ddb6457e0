class Devise::Mailer < Devise.parent_mailer.constantize
  include Devise::Mailers::Helpers
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def invitation_instructions(record, token, opts={})
    @token = token
    @invitation_email = record.email
    @url = generate_invite_url_with_email(token, @invitation_email)
    devise_mail(record, :invitation_instructions, opts)
  end

  def reset_password_instructions(record, token, opts={})
    @token = token
    @reset_password_url = generate_reset_password_url(token)
    devise_mail(record, :reset_password_instructions, opts)
  end

  private

  def generate_invite_url_with_email(token, email)
    "#{base_url}/accept_invite?invitation_token=#{token}&email=#{CGI.escape(email)}"
  end

  def generate_reset_password_url(token)
    edit_user_password_url(reset_password_token: token)
  end
end
