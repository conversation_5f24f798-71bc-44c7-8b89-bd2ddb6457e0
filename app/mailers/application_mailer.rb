class ApplicationMailer < ActionMailer::Base
  default from: "Motel Database Admin <no-reply@#{Rails.application.config.mailer_host}>"

  if !Rails.env.test?
    layout 'mailer'
  end

  def init_defaults
    @body_args = {}
  end

  before_action :init_defaults

  helper :format

  include Crypt<PERSON>elper

  prepend_view_path 'app/views/mailers'

  def mail(**args)
    if (email_unsubscription_key = args.delete(:email_unsubscription_key)).nil?
      super args
      return
    end

    if NotificationPreference.notification_events[email_unsubscription_key]
      @unsubscription_url = unsubscription_url(email_unsubscription_key)

      # See https://support.google.com/a/answer/81126?hl=en&visit_id=638471267709917353-1292823957&rd=1
      args['List-Unsubscribe'] = @unsubscription_url
    end

    mailer = super args

    if (preference = @user.notification_preferences.find_by(notification_event: email_unsubscription_key))
      mailer.perform_deliveries = preference.email_notification_allowed
    else
      notification_config = "#{email_unsubscription_key}_notification_config".camelize.constantize.new
      mailer.perform_deliveries = notification_config.default_email_recipient_roles.include?(@user.role.to_sym)
    end

    mailer.perform_deliveries = false if @user.unaccepted_invite?

    mailer
  end

  private

  def unsubscription_url(notification_event)
    unsubscribe_data = { user_id: @user.id, type: notification_event, expired_at: 7.days.from_now }
    # unsubscribe_data = { user_id: @user.id, type: notification_event, expired_at: 7.seconds.from_now }

    encrypted_message = encrypt_email_unsubscription(unsubscribe_data)

    new_email_unsubscription_url(k: encrypted_message)
  end
end
