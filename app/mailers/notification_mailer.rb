class NotificationMailer < ApplicationMailer
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def created
    @notification = params[:notification]
    @user = @notification.user
    @notification_event = @notification.notification_event

    @title = I18n.t(
      "notification_mailer.created.#{@notification_event}.title",
      **title_args,
      default: nil
    )
    @additional_info = I18n.t(
      "notification_mailer.created.#{@notification_event}.#{additional_info_key}",
      default: nil
    )
    @cta_url = cta_url
    @body_args = body_args

    mail(
      to: @user.email,
      subject: @title,
      template_name: 'generic',
      email_unsubscription_key: @notification.notification_event
    )
  end

  private

  def title_args
    {
      motel_name: @notification.target_motel&.name,
      alert_type: @notification.alert&.alert_type,
      upcase_alert_type: @notification.alert&.alert_type&.upcase,
    }
  end

  def body_args
    {
      motel_name: @notification.target_motel&.name,
      alert_type: @notification.alert&.alert_type,
      announcement_message: @notification.announcement&.message
    }
  end

  def additional_info_key
    @notification.suggestion_for_red_cohort? ? 'red_cohort_additional_info' : 'additional_info'
  end

  def cta_url
    return if @notification.notification_event == 'announcement_created'

    if (motel = @notification.target_motel)
      "#{base_url}/motel/#{motel.id}"
    else
      raise "No valid association found for notification: #{@notification.id}"
    end
  end
end
