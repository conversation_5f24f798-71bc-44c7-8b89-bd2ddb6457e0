if params[:non_primary].present?
  json.array! @non_primary_contacts do |contact|
    json.title contact.full_name
    json.subtitle contact.email
    json.value contact.id
  end
else
  @primary_contacts.each do |contact|
    json.child! do
      json.id contact.id
      json.title contact.full_name
      json.subtitle contact.email
      json.icon 'contact_mail'
      json.iconColor '#304FFE'
      json.chips do
        json.child! do
          json.icon 'contact_mail'
          json.color '#304FFE'
          json.text contact.primary_contact
        end
      end
      json.editActions do
        if current_user.admin?
          # json.child! do
          #   json.text 'Edit Primary Contact'
          #   json.icon 'edit'
          #   json.action 'edit'
          #   json.color '#1a237e'
          # end
          json.child! do
            json.text 'Remove Primary Contact'
            json.icon 'delete'
            json.action 'delete'
            json.color '#e53835'
          end
        end
      end
    end
  end
end
