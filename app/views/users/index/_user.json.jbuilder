json.id user.id
json.title user.full_name
json.subtitle user.email
json.new user.new_user?
json.icon role[:icon]
json.iconColor role[:iconColor]
json.partial! 'users/index/chips', user: user, chips: role[:chips]

if current_user.manager_or_admin?
  actions = role[:editActions]

  if current_user.manager?
    # The only action a manager can perform is archiving a standard user
    actions = user.role == 'user' ? actions.select { |action| action == 'archive' } : []
  end

  json.partial! 'users/index/edit_actions', user: user, actions: actions if actions.any?
end
