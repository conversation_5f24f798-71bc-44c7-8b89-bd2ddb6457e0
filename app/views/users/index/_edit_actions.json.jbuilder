current_user_is_not_user = current_user.id != user.id
update_role_options = User.roles.keys.reject { |role| role == user.role }.map do |role|
  { value: role, text: t("user.roles.#{role}") }
end

actions_array = []

actions_array << {
  action: 'unlock',
  text: 'Unlock Account',
  icon: 'lock_open',
  color: '#01695c',
} if actions.include?('unlock') && user.access_locked?

actions_array << {
  action: 'update_role',
  text: 'Update Role',
  icon: 'edit',
  color: '#1a237e',
  currentRole: t("user.roles.#{user.role}"),
  options: update_role_options
} if actions.include?('update_role') && current_user_is_not_user

actions_array << {
  action: 'archive',
  text: 'Archive User',
  icon: 'archive',
  color: '#e53835',
  id: user.id
} if actions.include?('archive') && current_user_is_not_user

actions_array << {
  action: 'resend_invite',
  text: 'Resend Invitation',
  icon: 'replay',
  color: '#1a237e',
  id: user.id
} if actions.include?('resend_invite') && user.invitation_sent_at.present?

actions_array << {
  action: 'cancel_invite',
  text: 'Cancel Invitation',
  icon: 'highlight_off',
  color: '#e53835',
  id: user.id
} if actions.include?('cancel_invite') && user.invitation_sent_at.present?

actions_array << {
  action: 'restore',
  text: 'Restore User',
  icon: 'restore',
  color: '#4caf50',
} if actions.include?('restore') && user.discarded?

json.set! :editActions, actions_array if actions_array.any?
