chips_array = []

chips_array << {
  icon: 'contact_mail',
  color: '#304FFE',
  backgroundColor: '#e7e9ff',
  text: user.primary_contact
} if chips.include?('primary') && user.primary_contact.present?

chips_array << {
  icon: 'lock',
  color: '#f2f2f2',
  backgroundColor: '#757575',
  text: 'Account Locked'
} if chips.include?('locked') && user.access_locked?

chips_array << {
  icon: 'lock_clock',
  color: '#000000',
  backgroundColor: '#C5CAE9',
  text: 'Password Expired'
} if chips.include?('expired_password') && user.password_needs_update?

chips_array << {
  icon: 'cancel',
  color: '#000000',
  backgroundColor: '#CFD8DC',
  text: 'Expired Invite'
} if chips.include?('expired_invite') && user.expired_invite?

chips_array << {
  icon: 'star',
  color: '#009688',
  backgroundColor: '#e0f2f0',
  text: 'Lead Admin'
} if user.lead_admin?

chips_array << {
  icon: 'lock',
  color: '#f2f2f2',
  backgroundColor: '#757575',
  text: 'Restricted Admin'
} if user.restricted_admin?

if chips.include?('archives_in') && user.invitation_sent_at < 60.days.ago && user.invitation_accepted_at.nil?
  days_until_archive = (90 - (Time.current - user.invitation_sent_at) / 1.day).to_i

  chips_array << {
    icon: 'archive',
    color: '#000000',
    backgroundColor: '#FCE8E6',
    text: "Invite Archives in #{days_until_archive} Days"
  }
end

json.set! :chips, chips_array if chips_array.any?
