user_roles = [
  {
    users: @standard,
    role: 'standard',
    title: 'Standard Users',
    icon: 'account_circle',
    iconColor: '#01579b',
    chips: ['primary', 'locked', 'expired_password'],
    editActions: ['unlock', 'update_role', 'archive'],
  },
  {
    users: @managers,
    role: 'manager',
    title: 'Managerial Admins',
    icon: 'supervised_user_circle',
    iconColor: '#01695c',
    chips: ['primary', 'locked', 'expired_password'],
    editActions: ['unlock', 'update_role', 'archive'],
  },
  {
    users: @admins,
    role: 'super_admin',
    title: 'Super-Admins',
    icon: 'admin_panel_settings',
    iconColor: '#1a237e',
    chips: ['primary', 'locked', 'expired_password'],
    editActions: ['unlock', 'update_role', 'archive'],
  }
]

user_roles.concat([
  {
    users: @pending,
    role: 'pending',
    title: 'Pending Invitations',
    icon: 'unsubscribe',
    iconColor: '#302527',
    chips: ['expired_invite', 'archives_in'],
    editActions: ['resend_invite', 'cancel_invite'],
  },
  {
    users: @archived,
    role: 'archived',
    title: 'Archived Users',
    icon: 'archive',
    iconColor: '#e53835',
    chips: [],
    editActions: ['restore'],
  }
]) if current_user.admin?

json.userRoles do
  json.array! user_roles.each do |role|
    json.role role[:role]
    json.title role[:title]
    json.icon role[:icon]
    json.iconColor role[:iconColor]
    json.count role[:users].count

    json.users do
      json.array! role[:users].each do |user|
        json.partial! 'users/index/user', user: user, role: role
      end
    end
  end
end
