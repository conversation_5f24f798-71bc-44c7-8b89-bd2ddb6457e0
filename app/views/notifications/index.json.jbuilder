json.nextPage @notifications.next_page unless @notifications.last_page?
json.notifications do
  @notifications.each do |notification|
    json.child! do
      title_args = {
        motel_name: notification.target_motel&.name || 'Accommodation Listing',
        alert_type: notification.alert&.alert_type,
      }
      message_args = {
        motel_name: notification.target_motel&.name,
        alert_type: notification.alert&.alert_type,
        announcement_message: notification.announcement&.message
      }

      json.id notification.id
      json.type notification.notification_event
      json.title t("notification.#{notification[:notification_event]}.title", **title_args)
      json.message t("notification.#{notification[:notification_event]}.body", **message_args)
      json.isRead notification.is_read
      json.timeAgo compact_time_ago(notification.created_at)

      icon_data = if ['alert_created', 'alert_updated'].include?(notification[:notification_event])
        { value: 'warning', color: '#fb8c00' }
      elsif ['caution_created', 'caution_updated'].include?(notification[:notification_event])
        { value: 'flag', color: '#000' }
      elsif notification[:notification_event] == 'announcement_created'
        { value: 'campaign', color: '#546E7A' }
      else
        {}
      end

      json.icon do
        json.merge!(icon_data)
      end

      json.url notification_url(notification, format: params[:format])
      json.motelId notification.target_motel&.id
      json.announcementId notification.announcement&.id
      json.alertId notification.alert&.id
      json.cautionId notification.caution&.id
      json.suggestionId notification.suggestion&.id
      if notification.review_required? && current_user.lead_admin? && params[:type] == 'review_required'
        json.reviewRequired true
      end
      json.rejected notification.rejected? && current_user.lead_admin?
    end
  end
end
