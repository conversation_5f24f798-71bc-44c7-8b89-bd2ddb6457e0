style = {
  backgroundColor: background_color,
  color: color,
  fontWeight: 'bold',
  borderRight: "1px solid #{divider_color}"
}

json.child! do
  json.title name
  json.key "#{name.downcase}_services"
  json.minWidth 200
  json.width 200
  json.headerProps do
    json.align 'start'
    json.style style
  end
  json.cellProps do
    json.align 'start'
    json.style do
      json.borderRight '1px solid #e0e0e0'
    end
  end
end
json.child! do
  json.title '#'
  json.key "#{name.downcase}_count"
  json.width 50
  json.minWidth 50
  json.headerProps do
    json.align 'center'
    json.style style
  end
  json.cellProps do
    json.style do
      json.borderRight '1px solid #e0e0e0'
    end
    json.align 'center'
  end
end
