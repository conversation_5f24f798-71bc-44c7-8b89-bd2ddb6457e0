json.fields do
  json.child! do
    json.type 'select_field'
    json.label 'Service Provider/List Name'
    json.options [{ id: 'all_motels', text: 'All Motels' }] +
      @all_services.map { |service| { id: service.id, text: service.name } }
    json.name 'service_id'
  end

  json.child! do
    json.type 'multi_select_field'
    json.label 'Organisation Types'
    json.options @all_organization_types.map { |organization_type|
      { id: organization_type.id, text: organization_type.name } }
    json.name 'organization_type_ids_csv'
  end

  json.child! do
    json.type 'multi_select_field'
    json.label 'Motel Characteristics'
    json.options @all_motel_characteristics.map { |amenity|
      { id: amenity.id, text: amenity.name } }
    json.name 'amenity_ids_csv'
  end

  json.child! do
    json.type 'select_field'
    json.label 'Sort by'
    json.options [
      { id: 'motel_name_asc', text: 'Motel Name (Ascending)' },
      { id: 'total_crossovers_desc', text: 'Total Crossovers (Descending)' }
    ]
    json.name 'sort_column'
    json.value @sort_column
  end
end

if @motels
  json.noDataText 'No accommodation found for the selected service provider/list'
  json.headers do
    json.child! do
      json.title 'Accommodation'
      json.align 'start'
      json.key 'accommodation'
      json.minWidth 220
      json.width 220
      json.fixed true
      json.headerProps do
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
      json.cellProps do
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
    end

    if @selected_organization_types
      @selected_organization_types.each do |organization_type|
        background_color, divider_color, color = audit_organization_type_colors(organization_type)

        render \
          'index_entity_heading',
          json: json,
          background_color: background_color,
          divider_color: divider_color,
          color: color,
          name: organization_type.name
      end
    elsif @selected_amenities
      @selected_amenities.each do |amenity|
        background_color, divider_color, color = audit_amenity_colors(amenity)

        render \
          'index_entity_heading',
          json: json,
          background_color: background_color,
          divider_color: divider_color,
          color: color,
          name: amenity.name
      end
    end

    json.child! do
      json.title 'Total'
      json.key 'total'
      json.width 50
      json.minWidth 50
      json.headerProps do
        json.align 'center'
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
      json.cellProps do
        json.align 'center'
      end
    end
  end

  column_totals = {}
  rows = []

  @motels.each do |motel|
    row = {
      accommodation: motel.name
    }

    total = 0
    count = 0

    if @selected_organization_types
      @selected_organization_types.each do |organization_type|
        services = motel.services.where(organization_type: organization_type)
        if @selected_service
          services = services.where.not(id: @selected_service)
        end
        services = services.to_a
        count = services.length
        total += count

        key = organization_type.name.downcase
        row[:"#{key}_services"] = count > 0 ? services.map(&:name).join(', ') : '-'
        row[:"#{key}_count"] = count

        column_totals[key] ||= 0
        column_totals[key] += count
      end
    elsif @selected_amenities
      @selected_amenities.each do |amenity|
        included_keys = [:yes]
        amenity_option_ids = amenity.amenity_options.to_a.map do |amenity_option|
          included_keys.include?(amenity_option.key) ? amenity_option.id : nil
        end.compact

        has_amenity = motel.motel_amenities.where(amenity_option_id: amenity_option_ids).any?
        count = has_amenity ? 1 : 0
        total += count

        key = amenity.name.downcase
        row[:"#{key}_services"] = has_amenity ? 'Yes' : 'No'
        row[:"#{key}_count"] = count

        column_totals[key] ||= 0
        column_totals[key] += count
      end
    end

    row[:total] = total

    rows << row
  end

  if @sort_column == :total_crossovers_desc
    rows = rows.sort_by { |row| -row[:total] }
  end

  json.rows do
    rows.each do |row|
      json.child! do
        json.merge! row
      end
    end

    if @motels.size > 0
      json.child! do
        json.accommodation 'Grand Total'

        total = 0
        column_totals.each do |key, count|
          json.set! "#{key}_count", count
          total += count
        end

        json.total total
      end
    end
  end
end
