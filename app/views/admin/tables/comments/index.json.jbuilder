json.commentStatus do
  json.child! do
    json.title 'Review Required'
    json.icon 'fact_check'
    json.count @review_required_comments.count
    json.items do
      json.array! @review_required_comments do |comment|
        json.id comment.id
        json.motelId comment.motel_id
        json.title comment.motel&.name
        json.subtitle comment.body
        json.subsubtitle format_date_time(comment.updated_at)
        json.iconColor 'warning'
      end
    end
  end
  json.child! do
    json.title 'Approved'
    json.icon 'check'
    json.count @approved_comments.count
    json.items do
      json.array! @approved_comments do |comment|
        json.id comment.id
        json.motelId comment.motel_id
        json.title comment.motel&.name
        json.subtitle comment.body
        json.subsubtitle format_date_time(comment.approved_at)
        json.iconColor 'success'
      end
    end
  end
  json.child! do
    json.title 'Declined'
    json.icon 'close'
    json.count @rejected_comments.count
    json.items do
      json.array! @rejected_comments do |comment|
        json.id comment.id
        json.motelId comment.motel_id
        json.title comment.motel&.name
        json.subtitle comment.body
        json.subsubtitle format_date_time(comment.rejected_at)
        json.iconColor 'error'
      end
    end
  end
end

json.rejectionReasonOptions do
  Comment.rejection_reasons.keys.each do |rejection_reason|
    json.child! do
      json.value rejection_reason
      json.text Comment.rejection_reason_text(rejection_reason)
    end
  end
end
