json.id @comment.id
json.motelId @comment.motel_id
json.motelName @comment.motel.name
json.activeStatus @comment.active_status.to_s.humanize
json.body @comment.body

json.date format_date(@comment.created_at)
json.time format_time(@comment.created_at)
json.user do
  json.name @comment.user_full_name
  json.email @comment.user.email
  json.avatar do
    json.text @comment.user.user_full_name_initials
  end
end

json.rejectionReason @comment.rejection_reason_text
json.rejectionNote @comment.rejection_note
