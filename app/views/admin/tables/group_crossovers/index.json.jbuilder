json.fields do
  json.child! do
    json.type 'multi_select_field'
    json.label 'Group1 Organisation Types'
    json.options @all_organization_types.map { |organization_type|
      { id: organization_type.id, text: organization_type.name } }
    json.name 'group1_organization_type_ids_csv'
  end

  json.child! do
    json.type 'multi_select_field'
    json.label 'Group1 Motel Characteristics'
    json.options @all_motel_characteristics.map { |amenity|
      { id: amenity.id, text: amenity.name } }
    json.name 'group1_amenity_ids_csv'
  end

  json.child! do
    json.type 'multi_select_field'
    json.label 'Group2 Organisation Types'
    json.options @all_organization_types.map { |organization_type|
      { id: organization_type.id, text: organization_type.name } }
    json.name 'group2_organization_type_ids_csv'
  end

  json.child! do
    json.type 'multi_select_field'
    json.label 'Group2 Motel Characteristics'
    json.options @all_motel_characteristics.map { |amenity|
      { id: amenity.id, text: amenity.name } }
    json.name 'group2_amenity_ids_csv'
  end

  json.child! do
    json.type 'select_field'
    json.label 'Crossover Type'
    json.options [
      { id: 'union', text: 'Option 1: Union' },
      { id: 'intersection', text: 'Option 2: Intersection' },
    ]
    json.name 'crossover_type'
  end
end

if @motels
  json.noDataText 'No accommodation found for the selected service provider/list'
  json.headers do
    json.child! do
      json.title 'Accommodation'
      json.align 'start'
      json.key 'accommodation'
      json.minWidth 220
      json.width 220
      json.fixed true
      json.headerProps do
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
      json.cellProps do
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
    end

    render \
      'index_group_heading',
      json: json,
      selected_organization_types: @group1_selected_organization_types,
      selected_amenities: @group1_selected_amenities,
      selected_organization_types_key: 'group1_organization_type_count',
      selected_amenities_key: 'group1_amenity_count',
      total_name: 'Comparison #1',
      total_key: 'group1_total'

    render \
      'index_group_heading',
      json: json,
      selected_organization_types: @group2_selected_organization_types,
      selected_amenities: @group2_selected_amenities,
      selected_organization_types_key: 'group2_organization_type_count',
      selected_amenities_key: 'group2_amenity_count',
      total_name: 'Comparison #2',
      total_key: 'group2_total'

    json.child! do
      json.title 'Crossover'
      json.key 'crossover'
      json.width 50
      json.minWidth 50
      json.headerProps do
        json.align 'center'
        json.style do
          json.backgroundColor '#f7f7f7'
          json.color '#000000'
          json.fontWeight 'bold'
        end
      end
      json.cellProps do
        json.align 'center'
      end
    end
  end

  rows = []

  @motels.each do |motel|
    row = {
      accommodation: motel.name
    }

    render \
      'index_group_row',
      json: json,
      selected_organization_types: @group1_selected_organization_types,
      selected_amenities: @group1_selected_amenities,
      selected_organization_types_key: 'group1_organization_type_count',
      selected_amenities_key: 'group1_amenity_count',
      total_key: 'group1_total',
      row: row,
      motel: motel,
      crossover_intersect_only: false

    render \
      'index_group_row',
      json: json,
      selected_organization_types: @group2_selected_organization_types,
      selected_amenities: @group2_selected_amenities,
      selected_organization_types_key: 'group2_organization_type_count',
      selected_amenities_key: 'group2_amenity_count',
      total_key: 'group2_total',
      row: row,
      motel: motel,
      crossover_intersect_only: @crossover_type == :intersection

    rows << row
  end

  rows = rows.sort_by { |row| -row['crossover'] }

  json.rows do
    rows.each do |row|
      json.child! do
        json.merge! row
      end
    end

    # if @motels.size > 0
    #   json.child! do
    #     json.accommodation 'Grand Total'

    #     total = 0
    #     column_totals.each do |key, count|
    #       json.set! "#{key}_count", count
    #       total += count
    #     end

    #     json.total total
    #   end
    # end
  end
end
