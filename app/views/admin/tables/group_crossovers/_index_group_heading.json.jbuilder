render \
  'index_entity_heading',
  json: json,
  background_color: '#01579B',
  divider_color: '#253c4f',
  color: '#fff',
  name: selected_organization_types.map(&:name).join(', '),
  key: selected_organization_types_key,
  min_width: 200

render \
  'index_entity_heading',
  json: json,
  background_color: '#00695C',
  divider_color: '#053f38',
  color: '#fff',
  name: selected_amenities.map(&:name).join(', '),
  key: selected_amenities_key,
  min_width: 200

render \
  'index_entity_heading',
  json: json,
  background_color: '#991b1a',
  divider_color: '#991b1a',
  color: '#fff',
  name: total_name,
  key: total_key,
  min_width: 50
