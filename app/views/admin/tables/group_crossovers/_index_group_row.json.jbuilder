total = 0
count = 0
selected_organization_types.each do |organization_type|
  services = motel.services.where(organization_type: organization_type)
  count += services.size
end
row[selected_organization_types_key] = count
total += count

count = 0
selected_amenities.each do |amenity|
  included_keys = [:yes]
  amenity_option_ids = amenity.amenity_options.to_a.map do |amenity_option|
    included_keys.include?(amenity_option.key) ? amenity_option.id : nil
  end.compact

  has_amenity = motel.motel_amenities.where(amenity_option_id: amenity_option_ids).any?
  count += has_amenity ? 1 : 0
end
row[selected_amenities_key] = count

case @crossover_type
when :union
  total += count
when :intersection
  if count > 0 && total > 0
    total += count
  else
    total = 0
  end
end

# if @crossover_type == :union || count > 0 && total > 0
#   total += count
# end
row[total_key] = total

# if @crossover_type == :intersection
#   total += count
#   row[total_key] = total
# end

crossover = row['crossover'] || 0

if crossover_intersect_only
  if crossover > 0 && total > 0
    crossover += total
  else
    crossover = 0
  end
else
  # row['crossover'] ||= 0
  crossover += total
end

# if crossover_intersect_only
#   if row['crossover'] > 0 && total > 0
#     row['crossover'] += total
#   end
# else
#   row['crossover'] ||= 0
#   row['crossover'] += total
# end

row['crossover'] = crossover
