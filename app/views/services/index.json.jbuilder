json.array! @organization_types do |organization_type|
  json.title organization_type.name
  json.services do
    json.array! organization_type.services.order(:name) do |service|
      json.title service.name
      if params[:accommodation_by_service_provider].present?
        json.accommodation do
          accommodation = service.motels.select(:name).order(:name)
          json.array! accommodation do |accommodation|
            json.title accommodation.name
          end
        end
      end
    end
  end
end
