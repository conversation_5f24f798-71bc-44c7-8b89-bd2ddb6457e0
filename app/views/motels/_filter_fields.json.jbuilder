json.fields do
  json.child! do
    json.type 'text_field'
    json.label 'Filter By Name'
    json.name 'name'
  end
  json.child! do
    json.type 'text_field'
    json.label 'Filter By Suburb or Postcode'
    json.name 'suburb_or_postcode'
  end
  json.child! do
    json.type 'grouped_multi_select_field'
    json.label 'Filter By Service Provider/List Name:'
    json.name 'services'
    json.options do
      @organization_types.each do |organization_type|
        if organization_type.services.any?
          json.child! do
            json.header "#{organization_type.name}:"
          end
          organization_type.services.order(:name).each do |service|
            json.child! do
              json.title service.name
              json.value service.id
            end
          end
        end
      end
    end
    json.value @filter['services'] if @filter && @filter['services'].present?
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Include Accommodation in Use By:'
    json.options @organization_types.map { |organization_type|
{ id: organization_type.id, text: organization_type.name } }
    json.name 'organization_types'
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Exclude Accommodation in Use By:'
    json.name 'exclude_organization_types'
    json.options @organization_types.map { |organization_type|
{ id: organization_type.id, text: organization_type.name } }
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Include Regions:'
    json.name 'regions'
    json.options Motel::REGIONS.map { |region| { id: region, text: region } }
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Exclude Regions:'
    json.name 'exclude_regions'
    json.options Motel::REGIONS.map { |region| { id: region, text: region } }
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Accommodation Type:'
    json.name 'motel_types'
    json.options Motel::MOTEL_TYPES.map { |motel_type| { id: motel_type, text: motel_type } }
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Density:'
    json.name 'densities'
    json.options Motel::DENSITIES.map { |density| { id: density, text: density_text(density) } }
  end
  json.child! do
    json.type 'multi_select_field'
    json.label 'Duration:'
    json.name 'durations'
    json.options Motel::DURATIONS.map { |duration| { id: duration, text: duration } }
  end
  json.child! do
    json.type 'checkbox_field'
    json.label "Display 'Unknown' Features in Search Results"
    json.name 'show_unknown_amenities'
    json.trueValue true
    json.falseValue false
  end
  if @motel_characteristics.present?
    json.child! do
      json.type 'chip_group'
      json.title 'Motel Characteristics'
      json.name 'motel_characteristics'
      json.chips do
        motel_characteristics_options = []
        @motel_characteristics.each do |motel_characteristic|
          motel_characteristics_options << motel_characteristic.amenity_options.reject do |option|
            option.key == :unknown || option.key == :no
          end
        end

        json.array! motel_characteristics_options.flatten.sort_by(&:display_text) do |motel_characteristics_option|
          json.id motel_characteristics_option.id
          json.text motel_characteristics_option.display_text
          json.helpText motel_characteristics_option.amenity.help_text
        end
      end
    end
  end
  if @safety_requirements.present?
    json.child! do
      json.type 'chip_group'
      json.title 'Safety Requirements'
      json.name 'safety_requirements'
      json.chips do
        @safety_requirements.each do |safety_requirement|
          options = safety_requirement.amenity_options.order(id: :asc).reject do |option|
            option.name == 'Unknown' || (option.name == 'No' && safety_requirement.name != 'Accepts Single Males') || (option.name == 'Yes' && safety_requirement.name == 'Accepts Single Males')
          end
          json.array! options do |safety_requirement_option|
            json.id safety_requirement_option.id
            json.text safety_requirement_option.display_text
          end
        end
      end
    end
  end
  if @main_amenities.present?
    json.child! do
      json.type 'chip_group'
      json.title 'Amenities/Features'
      json.name 'main_amenities'
      json.chips do
        @main_amenities.each do |main_amenity|
          json.array! main_amenity.amenity_options.order(id: :asc).reject { |option|
              option.name == 'Unknown' ||
              (option.name == 'No' && main_amenity.name != 'ID Required') ||
              (main_amenity.name == 'Accept Invoices' && option.name == 'Yes (Sometimes)') ||
              (main_amenity.name == 'ID Required' && option.name == 'Yes (Sometimes)') ||
              (main_amenity.name == 'ID Required' && option.name == 'Yes (Always)') ||
              (main_amenity.name == 'Laundry' && option.name == 'Yes (Free)')
            } do |main_amenity_option|
            json.id main_amenity_option.id
            if main_amenity.name == 'Laundry'
              json.text 'Laundry'
            else
              json.text main_amenity_option.display_text
            end
          end
        end
      end
    end
  end
  if @additional_amenities.present?
    json.child! do
      json.type 'chip_group'
      json.title 'Additional Amenities'
      json.name 'additional_amenities'
      json.chips do
        @additional_amenities.each do |additional_amenity|
          json.array! additional_amenity.amenity_options.order(id: :asc).reject { |option|
              option.name == 'Unknown'||
              option.name == 'No' ||
              (additional_amenity.name == 'Pet Friendly' && option.name == 'Yes (All Pets)') ||
              (additional_amenity.name == 'Wifi/Internet' && option.name == 'Yes (Free)')
            } do |additional_amenity_option|
            json.id additional_amenity_option.id
            if additional_amenity.name == 'Pet Friendly'
              json.text 'Pet Friendly'
            elsif additional_amenity.name == 'Wifi/Internet'
              json.text 'Wifi/Internet'
            elsif additional_amenity_option.display_text == 'Paid On-Site Parking'
              json.text 'On-Site Parking'
            else
              json.text additional_amenity_option.display_text
            end
          end
        end
      end
    end
  end
end
