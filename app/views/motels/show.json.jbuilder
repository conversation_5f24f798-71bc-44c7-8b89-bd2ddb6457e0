banners = []

banners << {
  type: 'inactive',
  icon: 'no_meeting_room',
  color: '#37474F'
} if @motel.inactive?

@motel.suggestions.kept.order(created_at: :desc).each do |suggestion|
  banners << {
    type: 'suggestion',
    id: suggestion.id,
    title: 'Suggestion',
    subtitle: suggestion.description,
    icon: 'real_estate_agent',
    color: suggestion.red_cohort? ? '#991b1a' : '#02838f'
  }
end

@motel.cautions.kept.order(created_at: :desc).each do |caution|
  banners << {
    type: 'caution',
    id: caution.id,
    title: 'Caution',
    subtitle: caution.description,
    icon: 'flag',
    color: '#000000'
  }
end

@motel.alerts.not_archived.order(created_at: :desc).each do |alert|
  banners << {
    type: 'alert',
    id: alert.id,
    title: "#{alert.alert_type} Alert#{' (Closed)' if alert.closed?}",
    subtitle: alert.description,
    icon: alert.closed? ? 'highlight_off' : 'warning',
    color: alert.alert_type_color,
  }
end

json.motel @motel
json.commentCount @motel.comments.approved.not_archived.count
json.banners banners if banners.any?
json.chipGroups do
  json.child! do
    json.title 'Type:'
    json.chips do
      json.child! do
        json.text @motel.motel_type
        json.color '#05a'
        json.backgroundColor '#e1f5fe'
      end
      json.child! do
        if @motel.density.present?
          json.text "#{@motel.density} Density"
          json.color '#05a'
          json.backgroundColor '#e1f5fe'
        else
          json.icon 'help'
          json.text 'Density'
          json.color '#000000'
          json.backgroundColor '#e0e0e0'
        end
      end
    end
  end
  json.child! do
    json.title 'Duration:'
    json.chips do
      @motel.duration.sort.each do |duration|
        json.child! do
          json.text duration
          json.color '#05a'
          json.backgroundColor '#e1f5fe'
        end
      end
    end
  end

  order = { 'green' => 1, 'yellow' => 2, 'red' => 3, 'gray' => 4, 'blue' => 5, 'danger' => 6, 'danger_secondary' => 7 }

  sorted_motel_characteristics = @motel_characteristics.sort_by do |chip|
    color_order = order[chip.amenity_option.color]
    name = chip.amenity_option.display_text
    [color_order, name]
  end
  if sorted_motel_characteristics.any?
    json.child! do
      json.title 'Motel Characteristics:'
      json.chips do
        sorted_motel_characteristics.each do |chip|
          json.child! do
            json.text chip.amenity_option.display_text
            json.amenityColor chip.amenity_option.color
            json.color amenity_option_text_color(chip.amenity_option)
            json.backgroundColor amenity_option_background_color(chip.amenity_option)
            json.borderColor amenity_option_border_color(chip.amenity_option)
            json.icon chip.amenity_option.icon
            json.note chip.note
          end
        end
      end
    end
  end

  json.child! do
    json.title 'Accommodation in Use By:'
    json.chips do
      @motel.sorted_organization_types.each do |organization_type|
        json.child! do
          json.text organization_type.name
          case organization_type.name
          when 'Access Point/IAP'
            json.backgroundColor '#01579B'
            json.color '#fff'
          when 'Local FV Support Service'
            json.backgroundColor '#00695C'
            json.color '#fff'
          when 'PUV/Justice Service'
            json.backgroundColor '#991b1a'
            json.color '#fff'
          when 'Safe Steps'
            json.backgroundColor '#7055c5'
            json.color '#fff'
          when 'The Orange Door'
            json.backgroundColor '#e57200'
            json.color '#fff'
          when 'Other (Books EA for Females/Victim-Survivors)'
            json.backgroundColor '#e0dbfd'
            json.color '#7055c5'
          when 'Other (Books EA for Males/Perpetrators)'
            json.backgroundColor '#fee6ec'
            json.color '#991b1a'
          when 'Salvation Army St Kilda Crisis Centre'
            json.backgroundColor '#e0e0e0'
            json.color '#000000'
          else
            json.backgroundColor '#e1f5fe'
            json.color '#05a'
          end
        end
      end
    end
  end
  json.child! do
    json.title 'Service Providers:'
    json.chips do
      @motel.services.each do |service|
        json.child! do
          json.text service.name
          case service.organization_type.name
          when 'Access Point/IAP'
            json.backgroundColor '#01579B'
            json.color '#fff'
          when 'Local FV Support Service'
            json.backgroundColor '#00695C'
            json.color '#fff'
          when 'PUV/Justice Service'
            json.backgroundColor '#991b1a'
            json.color '#fff'
          when 'Safe Steps'
            json.backgroundColor '#7055c5'
            json.color '#fff'
          when 'The Orange Door'
            json.backgroundColor '#e57200'
            json.color '#fff'
          when 'Other (Books EA for Females/Victim-Survivors)'
            json.backgroundColor '#e0dbfd'
            json.color '#7055c5'
          when 'Other (Books EA for Males/Perpetrators)'
            json.backgroundColor '#fee6ec'
            json.color '#991b1a'
          when 'Salvation Army St Kilda Crisis Centre'
            json.backgroundColor '#e0e0e0'
            json.color '#000000'
          else
            json.backgroundColor '#e1f5fe'
            json.color '#05a'
          end
        end
      end
    end
  end

  sorted_safety_requirements = @safety_requirements.sort_by do |chip|
    color_order = order[chip.amenity_option.color]
    name = chip.amenity_option.display_text
    [color_order, name]
  end
  if sorted_safety_requirements.any?
    json.child! do
        json.title 'Safety:'
        json.chips do
          sorted_safety_requirements.each do |chip|
            json.child! do
              json.text chip.amenity_option.display_text
              json.amenityColor chip.amenity_option.color
              json.color amenity_option_text_color(chip.amenity_option)
              json.backgroundColor amenity_option_background_color(chip.amenity_option)
              json.icon chip.amenity_option.icon
              json.note chip.note
            end
          end
        end
      end
  end
  sorted_main_amenities = @main_amenities.sort_by do |chip|
    color_order = order[chip.amenity_option.color]
    name = chip.amenity_option.display_text
    [color_order, name]
  end
  if sorted_main_amenities.any?
    json.child! do
      json.title 'Amenities/Features:'
      json.chips do
        sorted_main_amenities.each do |chip|
          json.child! do
            json.text chip.amenity_option.display_text
            json.amenityColor chip.amenity_option.color
            json.color amenity_option_text_color(chip.amenity_option)
            json.backgroundColor amenity_option_background_color(chip.amenity_option)
            json.icon chip.amenity_option.icon
            json.note chip.note
          end
        end
      end
    end
  end
  sorted_additional_amenities = @additional_amenities.sort_by do |chip|
    color_order = order[chip.amenity_option.color]
    name = chip.amenity_option.display_text
    [color_order, name]
  end
  if sorted_additional_amenities.any?
    json.child! do
      json.title 'Additional Amenities:'
      json.chips do
        sorted_additional_amenities.each do |chip|
          unless chip.amenity_option.display_text == 'No Other Amenities'
            json.child! do
              json.text chip.amenity_option.display_text
              json.amenityColor chip.amenity_option.color
              json.color amenity_option_text_color(chip.amenity_option)
              json.backgroundColor amenity_option_background_color(chip.amenity_option)
              json.icon chip.amenity_option.icon
              json.note chip.note
            end
          end
        end
      end
    end
  end
  if @unknown.any?
    json.child! do
      json.title 'Unknown:'
      json.chips do
        @unknown.sort.each do |chip|
          json.child! do
            json.text chip.amenity_option.display_text
            json.color amenity_option_text_color(chip.amenity_option)
            json.backgroundColor amenity_option_background_color(chip.amenity_option)
            json.icon chip.amenity_option.icon
            json.note chip.note
          end
        end
      end
    end
  end
end
