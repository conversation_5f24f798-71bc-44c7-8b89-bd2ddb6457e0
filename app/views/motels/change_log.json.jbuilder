
json.changeLogs do
  @grouped_change_logs.each do |performed_at, logs|
    json.child! do
      json.date format_date(performed_at)
      json.time format_time(performed_at)

      user = logs.first.user
      json.user do
        if user
          json.name user.full_name
          json.email user.email
        end
      end

      json.items do
        logs.each do |log|
          json.child! do
            action = activity_type_action(log.activity_type)
            json.action action
            json.message change_log_message(log)
            json.value change_log_value(log)
          end
        end
      end
    end
  end

  json.child! do
    json.date format_date(@motel.created_at)
    json.time format_time(@motel.created_at)

    json.items do
      json.child! do
        json.message "Accommodation listing created"
      end
    end
  end
end
