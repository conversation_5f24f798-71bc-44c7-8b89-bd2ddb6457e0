amenity_count = 0

def generate_fields(json, amenity, feature_count)
  motel_amenity = @motel.motel_amenities.find_by(amenity_id: amenity.id)

  json.child! do
    json.type 'field_group'
    json.fields do
      if motel_amenity.present?
        json.child! do
          json.type 'hidden_field'
          json.name "motel_amenities_attributes[#{feature_count}][id]"
          json.value motel_amenity.id
        end
      end
      json.child! do
        json.type 'hidden_field'
        json.name "motel_amenities_attributes[#{feature_count}][amenity_id]"
        json.value amenity.id
      end
      json.child! do
        json.type 'button_field'
        json.label amenity.name
        json.helpText amenity.help_text
        json.name "motel_amenities_attributes[#{feature_count}][amenity_option_id]"
        amenity_options = amenity.ordered_amenity_options.map { |option|
          { id: option.id, text: option.name }
        }
        json.options amenity_options
        unknown_option = amenity_options.find { |option| option[:text] == 'Unknown' } || amenity_options.find { |option|
          option[:text] == 'No' }
        json.value motel_amenity&.amenity_option_id || (unknown_option[:id] if unknown_option.present?)
      end
      json.child! do
        json.type 'text_area_field'
        json.label 'Note'
        json.name "motel_amenities_attributes[#{feature_count}][note]"
        json.value motel_amenity&.note
      end
    end
  end
end


json.forms do
  json.child! do
    json.title 'Accommodation Details'
    json.fields do
      json.child! do
        json.type 'text_field'
        json.label 'Accommodation Name*'
        json.name 'name'
        json.value @motel.name
        json.columnWidth do
          json.cols 12
          json.md 8
          json.sm 6
        end
        json.validation do
          json.required do
            json.message 'Name cannot be blank'
          end
        end
      end
      json.child! do
        json.type 'select_field'
        json.label 'Accommodation Type'
        json.name 'motel_type'
        json.clearable true
        json.options Motel::MOTEL_TYPES.map { |motel_type| { id: motel_type, text: motel_type } }
        json.value @motel.motel_type
        json.columnWidth do
          json.cols 12
          json.md 4
          json.sm 6
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Street*'
        json.name 'street'
        json.value @motel.street
        json.columnWidth do
          json.cols 12
          json.md 8
          json.sm 6
        end
        json.validation do
          json.required do
            json.message 'Street cannot be blank'
          end
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Suburb*'
        json.name 'suburb'
        json.value @motel.suburb
        json.columnWidth do
          json.cols 12
          json.md 4
          json.sm 6
        end
        json.validation do
          json.required do
            json.message 'Suburb cannot be blank'
          end
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Postcode*'
        json.name 'postcode'
        json.value @motel.postcode
        json.columnWidth do
          json.cols 12
          json.md 4
          json.sm 6
        end
        json.validation do
          json.required do
            json.message 'Postcode cannot be blank'
          end
        end
      end
      json.child! do
        json.type 'select_field'
        json.label 'Region*'
        json.name 'region'
        json.options Motel::REGIONS.map { |region| { id: region, text: region } }
        json.value @motel.region
        json.columnWidth do
          json.cols 12
          json.md 4
          json.sm 6
        end
        json.validation do
          json.required do
            json.message 'Region cannot be blank'
          end
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'State*'
        json.name 'state'
        json.value @motel.state || 'VIC'
        json.readonly true
        json.hint 'This field is read-only'
        json.columnWidth do
          json.cols 12
          json.md 4
          json.sm 6
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Phone'
        json.name 'phone'
        json.value @motel.phone
        json.columnWidth do
          json.cols 12
          json.md 6
          json.sm 6
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Email'
        json.name 'email'
        json.value @motel.email
        json.columnWidth do
          json.cols 12
          json.md 6
          json.sm 6
        end
      end
      json.child! do
        json.type 'text_field'
        json.label 'Website URL'
        json.name 'website'
        json.value @motel.website
        json.columnWidth do
          json.cols 12
          json.md 6
          json.sm 6
        end
      end
      json.child! do
        json.type 'select_field'
        json.label 'Duration'
        json.name 'duration'
        json.multiple true
        json.value @motel.duration
        json.clearable true
        json.options Motel::DURATIONS.map { |duration| { id: duration, text: duration } }
        json.columnWidth do
          json.cols 12
          json.md 6
          json.sm 6
        end
      end
      json.child! do
        json.type 'select_field'
        json.label 'Density'
        json.name 'density'
        json.value @motel.density
        json.clearable true
        json.options Motel::DENSITIES.map { |density| { id: density, text: density_text(density) } }
        json.columnWidth do
          json.cols 12
          json.md 6
          json.sm 6
        end
      end
    end
  end

  json.child! do
    json.title 'Accommodation in Use By'
    json.fields do
      @organization_types.each_with_index do |organization_type, index|
        if organization_type.services.any?
          motel_services_ids = @motel.motel_services
            .joins(:service)
            .where(services: { organization_type_id: organization_type.id })
            .pluck(:service_id)
          json.child! do
            json.type 'select_field'
            json.label "#{organization_type.name}:"
            json.multiple true
            json.columnWidth do
              json.cols 12
              json.md 6
              json.sm 6
            end
            json.name "motel_services_by_organization_type[#{index}]"
            json.options organization_type.services.order(:name).map { |service|
              { id: service.id, text: service.name } }
            json.value motel_services_ids
            json.clearable true
          end
        end
      end
    end
  end

  json.child! do
    json.title 'Motel Characteristics'
    json.fields do
      @motel_characteristics.each do |amenity|
        generate_fields(json, amenity, amenity_count)
        amenity_count += 1
      end
    end
  end
  json.child! do
    json.title 'Safety'
    json.fields do
      @safety_requirements.each do |amenity|
        generate_fields(json, amenity, amenity_count)
        amenity_count += 1
      end
    end
  end
  json.child! do
    json.title 'Amenities/Features'
    json.fields do
      @main_amenities.each do |amenity|
        generate_fields(json, amenity, amenity_count)
        amenity_count += 1
      end
    end
  end
  json.child! do
    json.title 'Additional Amenities'
    json.fields do
      @additional_amenities.each do |amenity|
        generate_fields(json, amenity, amenity_count)
        amenity_count += 1
      end
      generate_fields(json, @other, amenity_count) if @other.present?
    end
  end
end
