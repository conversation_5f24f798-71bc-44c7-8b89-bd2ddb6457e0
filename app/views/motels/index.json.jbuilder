if params[:only_filter_fields].present?
  json.partial! 'motels/filter_fields'
else
  json.nextPage @motels.next_page unless @motels.last_page?
  json.currentPage @motels.current_page

  json.counts @counts

  json.motels do
    json.array! @motels do |motel|
      json.id motel.id
      json.name motel.name
      json.motelType motel.motel_type
      json.region motel.region
      json.street motel.street
      json.suburb motel.suburb
      json.postcode motel.postcode
      json.density motel.density
      json.duration motel.duration
      json.phone motel.phone
      json.email motel.email
      json.commentCount motel.comments.approved.not_archived.count
      json.alertCount motel.alerts.not_archived.count
      json.inactive motel.inactive?
      json.cautionCount motel.cautions.kept.count
      json.suggestionCount motel.suggestions.kept.count
      json.suggestionColor motel.red_suggestion? ? '#991b1b' : '#02838f'
    end
  end
end
