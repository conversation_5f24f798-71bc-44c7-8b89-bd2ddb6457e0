json.id @caution.id
json.motelId @caution.motel_id
json.motelName @caution.motel.name
json.title 'Caution'
json.authorisedBy @caution.authorised_by
json.archived @caution.archived?
if @caution.caution_histories.present?
  most_recent_edit = @caution.caution_histories.last
  json.description most_recent_edit.description
  json.subtitle most_recent_edit.created_at.in_time_zone.strftime('%d/%m/%y')
  json.edited true
  json.date format_date(most_recent_edit.created_at)
  json.time format_time(most_recent_edit.created_at)
  json.user do
    json.name most_recent_edit.user_full_name
    json.orgTitle most_recent_edit.user_position_organization
    json.email most_recent_edit.user.email
    json.avatar do
      json.text most_recent_edit.user.user_full_name_initials
    end
  end
  json.cautionHistories do
    @caution.caution_histories.order(created_at: :desc).each do |history|
      unless history.id == most_recent_edit.id
        json.child! do
          json.name history.user_full_name
          json.email history.user.email
          json.avatar do
            json.text history.user.user_full_name_initials
          end
          json.orgTitle history.user_position_organization
          json.date format_date(history.created_at)
          json.time format_time(history.created_at)
          json.description history.description
        end
      end
    end
    json.child! do
      json.name @caution.user_full_name
      json.email @caution.user.email
      json.avatar do
        json.text @caution.user.user_full_name_initials
      end
      json.orgTitle @caution.user_position_organization
      json.date format_date(@caution.created_at)
      json.time format_time(@caution.created_at)
      json.description @caution.description
    end
  end
else
  json.subtitle format_date(@caution.created_at)
  json.subsubtitle @caution.description
  json.date format_date(@caution.created_at)
  json.time format_time(@caution.created_at)
  json.description @caution.description
  json.user do
    json.name @caution.user_full_name
    json.orgTitle @caution.user_position_organization
    json.email @caution.user.email
    json.avatar do
      json.text @caution.user.user_full_name_initials
    end
  end
end
