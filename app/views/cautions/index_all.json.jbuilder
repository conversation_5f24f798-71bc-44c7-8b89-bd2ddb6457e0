json.array! @cautions do |caution|
  json.id caution.id
  json.motelId caution.motel_id
  json.title caution&.motel&.name
  if caution.caution_histories.present?
    most_recent_edit = caution.caution_histories.last
    json.subsubtitle most_recent_edit.description
    json.subtitle most_recent_edit.created_at.in_time_zone.strftime('%d/%m/%y')
  else
    json.subtitle caution.created_at.in_time_zone.strftime('%d/%m/%y')
    json.subsubtitle caution.description
  end
  json.discarded caution.discarded?
  
  json.editable caution.editable?(current_user)
end
