json.alertStatus do
  json.child! do
    json.title 'Open'
    json.icon 'warning'
    json.count @open.count
    json.items do
      json.array! @open do |alert|
        json.id alert.id
        json.motelId alert.motel_id
        json.title alert&.motel&.name
        json.subtitle alert.description
        json.subsubtitleIcon 'edit' if alert.alert_histories.any?
        json.subsubtitle alert.alert_updated_date.in_time_zone.strftime('%d/%m/%y')
        json.alertType alert.alert_type
        json.alertTypeColor alert.alert_type_color
        json.closed alert.closed?
      end
    end
  end
  json.child! do
    json.title 'Closed'
    json.icon 'highlight_off'
    json.count @closed.count
    json.items do
      json.array! @closed do |alert|
        json.id alert.id
        json.motelId alert.motel_id
        json.title alert&.motel&.name
        json.subsubtitleIcon 'highlight_off'
        json.subsubtitle alert.closed_at.in_time_zone.strftime('%d/%m/%y')
        json.subtitle alert.description
        json.alertType alert.alert_type
        json.alertTypeColor alert.alert_type_color
        json.closed alert.closed?
      end
    end
  end
  json.child! do
    json.title 'Archived'
    json.icon 'archive'
    json.count @archived.count
    json.items do
      json.array! @archived do |alert|
        json.id alert.id
        json.motelId alert.motel_id
        json.title alert&.motel&.name
        json.subtitle alert.description
        json.subsubtitleIcon 'archive'
        json.subsubtitle alert.archived_at.in_time_zone.strftime('%d/%m/%y')
        json.alertType alert.alert_type
        json.alertTypeColor alert.alert_type_color
        json.closed alert.closed?
      end
    end
  end
end