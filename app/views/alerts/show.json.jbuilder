json.id @alert.id
json.motelId @alert.motel_id
json.motelName @alert.motel.name
json.icon @alert.closed? ? 'highlight_off' : 'warning'
json.color @alert.alert_type_color
json.type @alert.alert_type
json.description @alert.description
json.reportedBy @alert.reported_by
json.witnesses @alert.witnesses
json.archived @alert.archived?
json.actions do
  @alert.alert_actions.each do |action|
    json.child! do
      json.name action.name
    end
  end
end
json.location @alert.location
json.incidentDateTime "#{format_time(@alert.time)} #{format_date(@alert.date)} "
json.user do
  json.name @alert.user_full_name
  json.email @alert.user.email
  json.orgTitle @alert.user_position_organization
  json.avatar do
    json.text @alert.user.user_full_name_initials
  end
end
json.date format_date(@alert.created_at)
json.time format_time(@alert.created_at)
json.histories do
  @alert.alert_histories.order(created_at: :desc).each do |history|
    json.child! do
      json.date format_date(history.created_at)
      json.time format_time(history.created_at)
      json.description history.description
      json.user do
        json.name history.user_full_name
        json.orgTitle history.user_position_organization
        json.email history.user.email
        json.avatar do
          json.text history.user.user_full_name_initials
        end
      end
    end
  end
end
if @alert.closed?
  json.closed do
    json.date format_date(@alert.closed_at)
    json.time format_time(@alert.closed_at)
    json.closureNote @alert.closure_note
    json.cautionIssued @alert.caution_issued
    json.user do
      json.name @alert.closed_user_full_name
      json.orgTitle @alert.closed_user_position_organization
      json.email @alert.closed_user.email
      json.avatar do
        json.text @alert.closed_user.user_full_name_initials
      end
    end
  end
end
