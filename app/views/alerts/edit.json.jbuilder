json.alertType @alert.alert_type
json.cautionIssued @alert.caution_issued
json.motelId @alert.motel_id
json.closureNote @alert.closure_note
json.date @alert.date.in_time_zone.strftime('%d/%m/%Y')
json.description @alert.description
json.histories do
  @alert.alert_histories.each do |history|
    json.child! do
      json.description history.description
    end
  end
end
json.location @alert.location
json.reportedBy @alert.reported_by
json.time @alert.time.in_time_zone.strftime('%I:%M %p').gsub(/^0/, '')
json.witnesses @alert.witnesses
json.actions do
  @alert.alert_actions.each do |action|
    json.child! do
      json.name action.name
    end
  end
end
