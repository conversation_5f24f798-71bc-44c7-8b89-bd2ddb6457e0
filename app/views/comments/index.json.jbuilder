json.commentCount @motel.comments.approved.not_archived.count
json.comments do
  @comments.each do |comment|
    unless comment.archived && current_user.user?
      json.child! do
        json.id comment.id
        json.avatar do
          json.text comment.user.user_full_name_initials
        end
        json.pinned comment.pinned
        json.name comment.user_full_name
        json.email comment.user.email
        json.orgTitle comment.user_position_organization
        json.date format_date(comment.latest_timestamp)
        json.time format_time(comment.latest_timestamp)
        if comment.comment_histories.present?
          most_recent_edit = comment.comment_histories.last
          json.edited true
          if comment.user.id != most_recent_edit.user.id
            json.editUser do
              json.name most_recent_edit.user_full_name
              json.orgTitle most_recent_edit.user_position_organization
            end
          end
          json.body most_recent_edit.body
          json.commentHistories do
            comment.comment_histories.order(created_at: :desc).each do |history|
              unless history.id == most_recent_edit.id
                json.child! do
                  json.name history.user_full_name
                  json.email history.user.email
                  json.avatar do
                    json.text history.user.user_full_name_initials
                  end
                  json.orgTitle history.user_position_organization
                  json.date format_date(history.created_at)
                  json.time format_time(history.created_at)
                  json.body history.body
                end
              end
            end
            json.child! do
              json.name comment.user_full_name
              json.email comment.user.email
              json.avatar do
                json.text comment.user.user_full_name_initials
              end
              json.orgTitle comment.user_position_organization
              json.date format_date(comment.created_at)
              json.time format_time(comment.created_at)
              json.body comment.body
            end
          end
        else
          json.body comment.body
        end
        json.editActions do
          if comment.editable?(current_user)
            json.child! do
              json.action 'edit'
              json.text 'Edit'
              json.icon 'edit'
              json.color '#fb8c00'
              json.originalUser true if comment.user == current_user
              json.id comment.id
              json.organisation comment.user_organization_title
              json.position comment.user_position_title
            end
          end
          unless comment.archived || current_user.user?
            json.child! do
              json.action 'pin'
              if comment.pinned
                json.color '#e53835'
                json.text 'Unpin'
              else
                json.color '#1a237e'
                json.text 'Pin'
              end
              json.icon 'push_pin'
            end
          end
          if current_user.admin?
            json.child! do
              json.action 'archive'
              if comment.archived
                json.text 'Unarchive'
                json.icon 'unarchive'
                json.color '#44a047'
              else
                json.text 'Archive'
                json.icon 'archive'
                json.color '#464646'
              end
            end
          end
        end
        json.archived comment.archived
        json.editable comment.editable?(current_user)
        json.reviewRequired comment.review_required?
      end
    end
  end
end
