json.array! @alert_logs do |alert_log|
  json.id alert_log.alert_id
  json.motelId alert_log.alert.motel_id
  json.title alert_log.log_description
  json.user do
    json.name alert_log.user_full_name
    json.orgTitle alert_log.user_position_organization
  end
  json.alertType alert_log.alert.alert_type
  json.alertTypeColor alert_log.alert.alert_type_color
  json.closed alert_log.alert.closed?
  json.archived alert_log.alert.archived?
  json.date alert_log.created_at.in_time_zone.strftime('%d/%m/%y')
  json.time alert_log.created_at.in_time_zone.strftime('%I:%M %p').gsub(/^0/, '')
end