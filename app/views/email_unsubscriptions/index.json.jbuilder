json.notificationEvents do
  @notification_groups.each do |group, events|
    json.child! do
      json.type 'checkbox_group'
      json.label t("notification_preference.setting.all_#{group}.title")

      all_allowed = events.all? { |event_data| event_data[:preference]&.email_notification_allowed || false }

      json.value all_allowed

      json.events do
        events.each do |event_data|
          event = event_data[:event]
          preference = event_data[:preference]

          json.child! do
            json.label t("notification_preference.setting.#{event}.title")
            json.name event
            json.value preference&.email_notification_allowed || false
            json.id preference&.id
          end
        end
      end
    end
  end
end
