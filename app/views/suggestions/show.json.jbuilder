json.id @suggestion.id
json.motelId @suggestion.motel_id
json.motelName @suggestion.motel.name
json.title 'Suggestion'
json.archived @suggestion.archived?
json.color @suggestion.red_cohort? ? '#991b1b' : '#02838f'
json.authorisedBy @suggestion.authorised_by
json.clientCohorts @suggestion.client_cohorts.order(name: :asc).pluck(:name)
if @suggestion.suggestion_histories.present?
  most_recent_edit = @suggestion.suggestion_histories.last
  json.description most_recent_edit.description
  json.subtitle most_recent_edit.created_at.in_time_zone.strftime('%d/%m/%y')
  json.edited true
  json.date format_date(most_recent_edit.created_at)
  json.time format_time(most_recent_edit.created_at)
  json.user do
    json.name most_recent_edit.user_full_name
    json.orgTitle most_recent_edit.user_position_organization
    json.email most_recent_edit.user.email
    json.avatar do
      json.text most_recent_edit.user.user_full_name_initials
    end
  end
  json.suggestionHistories do
    @suggestion.suggestion_histories.order(created_at: :desc).each do |history|
      unless history.id == most_recent_edit.id
        json.child! do
          json.name history.user_full_name
          json.email history.user.email
          json.avatar do
            json.text history.user.user_full_name_initials
          end
          json.orgTitle history.user_position_organization
          json.date format_date(history.created_at)
          json.time format_time(history.created_at)
          json.description history.description
        end
      end
    end
    json.child! do
      json.name @suggestion.user_full_name
      json.email @suggestion.user.email
      json.avatar do
        json.text @suggestion.user.user_full_name_initials
      end
      json.orgTitle @suggestion.user_position_organization
      json.date format_date(@suggestion.created_at)
      json.time format_time(@suggestion.created_at)
      json.description @suggestion.description
    end
  end
else
  json.subtitle format_date(@suggestion.created_at)
  json.subsubtitle @suggestion.description
  json.date format_date(@suggestion.created_at)
  json.time format_time(@suggestion.created_at)
  json.description @suggestion.description
  json.user do
    json.name @suggestion.user_full_name
    json.orgTitle @suggestion.user_position_organization
    json.email @suggestion.user.email
    json.avatar do
      json.text @suggestion.user.user_full_name_initials
    end
  end
end
