json.array! @suggestions do |suggestion|
  json.id suggestion.id
  json.motelId suggestion.motel_id
  json.title suggestion&.motel&.name
  json.color suggestion.red_cohort? ? '#991b1b' : '#02838f'
  if suggestion.suggestion_histories.present?
    most_recent_edit = suggestion.suggestion_histories.last
    json.subsubtitle most_recent_edit.description
    json.subtitle most_recent_edit.created_at.in_time_zone.strftime('%d/%m/%y')
  else
    json.subtitle suggestion.created_at.in_time_zone.strftime('%d/%m/%y')
    json.subsubtitle suggestion.description
  end
  json.discarded suggestion.discarded?
  
  json.editable suggestion.editable?(current_user)
end
