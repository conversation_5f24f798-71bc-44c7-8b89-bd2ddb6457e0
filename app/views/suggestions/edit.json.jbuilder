json.id @suggestion.id
json.accommodation @suggestion.motel.name
json.accommodationId @suggestion.motel.id
json.authorisedBy @suggestion.authorised_by
json.clientCohorts @suggestion.suggestion_client_cohorts.pluck(:client_cohort_id)
if @suggestion.suggestion_histories.present?
  most_recent_edit = @suggestion.suggestion_histories.last
  json.description most_recent_edit.description
else
  json.description @suggestion.description
end