class NotificationRejectionsController < ApplicationController
  before_action :authenticate_user!

  authorize_resource class: false

  def create
    rejected_notification = Notification.review_required.find(params[:notification_id])

    event_group_key = rejected_notification.event_group_key

    # When a lead admin rejects a notification, also reject the relevant notifications of other lead admins
    Notification.review_required.where(event_group_key: event_group_key).find_each do |notification|
      notification.rejected!
    end
  end
end
