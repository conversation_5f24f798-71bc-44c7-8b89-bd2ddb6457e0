class CommentsController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :motel
  load_and_authorize_resource :comment, through: :motel

  def index
    review_required_condition = Comment.review_required
    # Lead admin can see other people's pending comments.
    unless current_user.lead_admin?
      review_required_condition = review_required_condition.where(user: current_user)
    end
    @comments = Comment \
      .approved
      .or(review_required_condition)
      .with_latest_timestamp_for(@motel)
  end

  def show
    # Just a proof of concept
    # Notification.mark_as_read(current_user, comment: @comment)

    render json: @comment
  end

  def new_form
  end

  def create
    @comment = @motel.comments.new(comment_params)
    @comment.user_id = current_user.id
    @comment.motel_id = @motel.id
    @comment.active_status = :review_required

    if @comment.save
      CreateNotificationJob.perform_later(:comment_review_required, current_user.id, comment: @comment)

      render json: {
        status: 'success',
        message: "Thank you for posting a comment!\n\n \n\n" + %(
          All comments are reviewed by a database administrator to ensure they are in line with the Terms of Use. You will receive an email notifying you if the comment has been approved or declined.
        ).squish
      }, status: :created
    else
      render json: @comment.errors, status: :unprocessable_entity
    end
  end

  def pin
    @comment.pinned = !@comment.pinned
    if @comment.save
      render json: @comment, status: :ok
    else
      render json: @comment.errors, status: :unprocessable_entity
    end
  end

  def archive
    @comment.archived = !@comment.archived
    if @comment.save
      CreateNotificationJob.perform_later(
        @comment.archived ? :comment_discarded : :comment_undiscarded, current_user.id, comment: @comment)

      render json: @comment, status: :ok
    else
      render json: @comment.errors, status: :unprocessable_entity
    end
  end

  private

  def comment_params
    params.require(:comment).permit(
      :archived,
      :body,
      :pinned,
      :user_position_title,
      :user_organization_title,
      :motel_id,
      :user_id
    )
  end
end
