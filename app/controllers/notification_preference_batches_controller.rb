# Define the notifications as a collection-based resource with its 7 own RESTful actions.
# Inspired from https://stackoverflow.com/questions/7965949/best-practice-for-bulk-update-in-controller
# Guiding reference: https://ics.uci.edu/~fielding/pubs/dissertation/rest_arch_style.htm#sec_5_2_1_1
class NotificationPreferenceBatchesController < ApplicationController
  before_action :authenticate_user!

  def update
    errors = []
    NotificationPreference.transaction do
      update_params[:notification_preferences].each do |preference_params|
        preference = if preference_params[:id].present?
          current_user.notification_preferences.find_by(id: preference_params[:id])
        else
          current_user.notification_preferences.find_or_initialize_by(notification_event: preference_params[:notification_event])
        end

        unless preference.update(email_notification_allowed: preference_params[:email_notification_allowed])
          errors << { id: preference.id, errors: preference.errors.full_messages }
        end
      end
    end

    if errors.any?
      render json: { success: false, message: "Failed to update email settings.", errors: errors }, status: :unprocessable_entity
    else
      render json: { success: true, message: "Settings updated successfully!" }, status: :ok
    end
  end


  private

  def update_params
    params.require(:notification_preference_batch).permit(
      notification_preferences: [
        :id,
        :email_notification_allowed,
        :notification_event
      ]
    )
  end
end
