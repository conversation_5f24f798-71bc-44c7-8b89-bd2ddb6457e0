# frozen_string_literal: true

class Users::PasswordsController < Devise::PasswordsController
  include RackSessionFix
  # POST /resource/password
  def create
    email = params[:email].downcase
    self.resource = resource_class.find_by("LOWER(email) = ?", email)
    admin_setting = AdminSetting.first
    if resource && admin_setting.reset_passcode == params[:reset_passcode]
      resource.send_reset_password_instructions
      yield resource if block_given?
      
      if successfully_sent?(resource)
        respond_with({}, location: after_sending_reset_password_instructions_path_for(resource_name))
      else
        respond_with(resource)
      end
    else
      respond_with(resource, status: :unprocessable_entity, location: new_password_path(resource_name))
    end
  end
end
