class Users::SessionsController < Devise::SessionsController
  include RackSessionFix
  # include ActionController::Cookies
  respond_to :json

  def create
    self.resource = warden.authenticate!(auth_options)
    if resource.errors.empty?
      sign_in(resource_name, resource)

      if resource.password_needs_update?
        render json: {
          message: 'Your password has expired. Please update your password.',
          password_needs_update: true
        }, status: :ok
      else
        if current_user.admin?
          cookies.encrypted[:mm_user] = {
            value: { user_id: current_user.id },
            expires: 1.hour,
            secure: Rails.env.production?,
            httponly: true
          }
        end

        yield resource if block_given?
        respond_with resource, location: after_sign_in_path_for(resource)
      end
    else
      respond_with_failed_attempt(resource)
    end
  end

  private

  def respond_with(resource, _opts = {})
    render json: {
      status: { code: 200, message: 'Logged in sucessfully.' },
    }, status: :ok
  end

  def respond_to_on_destroy
    if current_user
      render json: {
        status: 200,
        message: 'Logged out sucessfully.'
      }, status: :ok
    else
      render json: {
        status: 401,
        message: 'Couldn\'t find an active session.'
      }, status: :unauthorized
    end
  end

  def respond_with_failed_attempt(resource)
    if resource.access_locked?
      respond_with_locked_account(resource)
    else
      attempts_left = Devise.maximum_attempts - resource.failed_attempts
      render json: {
        status: 401,
        message: "Login failed. You have #{attempts_left} attempts left before your account is locked."
      }, status: :unauthorized
    end
  end

  def respond_with_locked_account(resource)
    render json: {
      status: 403,
      message: "Your account is locked. Please contact an admin to unlock your account."
    }, status: :forbidden
  end
end
