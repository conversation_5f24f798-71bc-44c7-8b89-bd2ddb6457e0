class Users::Invitations<PERSON>ontroller < Devise::InvitationsController
  before_action :authenticate_user!
  before_action :authorize_admin!, except: :update

  def create
    self.resource = invite_resource
    existing_user = resource.invitation_accepted_at.present?
    if existing_user
      render json: { status: 'error', message: 'User with this email address already has an account' }, status: :unprocessable_entity
    elsif resource.errors.empty?
      render json: { status: 'success', message: 'Invitation sent successfully' }, status: :ok
    else
      render json: { status: 'error', message: resource.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    self.resource = resource_class.accept_invitation!(update_resource_params)
    
    if resource.errors.empty?
      render json: { status: 'success', message: 'Password successfully set' }, status: :ok
    else
      render json: { status: 'error', message: resource.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def resend_invitation
    user = User.find_by(id: params[:id])
    
    if user.nil?
      render json: { status: 'error', message: 'User not found' }, status: :not_found
      return
    end

    if user.invitation_accepted_at.present?
      render json: { status: 'error', message: 'User has already accepted the invitation' }, status: :unprocessable_entity
      return
    end

    user.invite! do |u|
      u.skip_invitation = true
    end
    user.invitation_sent_at = Time.current
    user.save
    user.deliver_invitation

    if user.errors.empty?
      render json: { status: 'success', message: 'Invitation resent successfully' }, status: :ok
    else
      render json: { status: 'error', message: user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def cancel_invitation
    user = User.find_by(id: params[:id])
    
    if user.nil?
      render json: { status: 'error', message: 'User not found' }, status: :not_found
      return
    end

    if user.invitation_accepted_at.present?
      render json: { status: 'error', message: 'User has already accepted the invitation' }, status: :unprocessable_entity
      return
    end

    if user.destroy
      render json: { status: 'success', message: 'Invitation deleted successfully' }, status: :ok
    else
      render json: { status: 'error', message: user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def invite_resource
    self.resource = resource_class.invite!(invite_resource_params, current_inviter)
    resource
  end

  def invite_resource_params
    params.require(:user).permit(:first_name, :last_name, :email)
  end

  def update_resource_params
    params.require(:user).permit(:password, :password_confirmation, :invitation_token)
  end

  def authorize_admin!
    render json: { status: 'error', message: 'Only admin can send invites' }, status: :unauthorized unless current_user.admin?
  end
end
