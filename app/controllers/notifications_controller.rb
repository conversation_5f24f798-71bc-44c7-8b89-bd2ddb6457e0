class NotificationsController < ApplicationController
  before_action :authenticate_user!

  def index
    query = current_user.notifications

    if params[:count].present?
      approval_status = current_user.lead_admin? ? ['review_required', 'approved'] : 'approved'
      notifications_count = query.unread.where(approval_status: approval_status).count

      render json: { status: 'success', count: notifications_count }
      return
    end

    case params[:type]
    when 'alert'
      query = query.alert_notifications
    when 'caution'
      query = query.caution_notifications
    when 'suggestion'
      query = query.suggestion_notifications
    when 'unread'
      query = query.unread
    when 'announcement'
      query = query.announcement_created
    end

    if params[:type] == 'review_required'
      query = query.review_required
    elsif params[:type] == 'history'
      # show all notifications, including self_triggered, approved, rejected, and review_required
    else
      query = query.approved
    end

    @notifications = query.
      order(created_at: :desc).
      page(params[:page]).
      per(50)

    respond_to :json
  end

  def show
    @notification = current_user.notifications.find(params[:id])
    @notification.update!(is_read: true)

    notifications_count = current_user.notifications.unread.approved.count
    render json: { status: 'success', count: notifications_count }
  end
end
