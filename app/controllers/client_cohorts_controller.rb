class ClientCohortsController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource

  def index
    @client_cohorts = ClientCohort.all.order(:name).map { |cohort| { value: cohort.id, title: cohort.name } }
    render json: @client_cohorts, status: :ok
  end

  # def show
  #   @client_cohort = ClientCohort.find(params[:id])
  # end

  # def create
  #   @client_cohort = ClientCohort.new(client_cohort_params)
  #   if @client_cohort.save
  #     render :show, status: :created
  #   else
  #     render json: @client_cohort.errors, status: :unprocessable_entity
  #   end
  # end

  # def update
  #   @client_cohort = ClientCohort.find(params[:id])
  #   if @client_cohort.update(client_cohort_params)
  #     render :show, status: :ok
  #   else
  #     render json: @client_cohort.errors, status: :unprocessable_entity
  #   end
  # end

  private

  def client_cohort_params
    params.require(:client_cohort).permit(:name, :red)
  end
end
