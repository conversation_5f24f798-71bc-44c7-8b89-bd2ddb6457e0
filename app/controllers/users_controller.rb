class UsersController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource

  def index
    @users = User.kept.order(:first_name, :last_name, :email).where.not(invitation_accepted_at: nil)
    @standard = @users.where(role: 'user')
    @managers = @users.where(role: 'manager')
    @admins = @users.where(role: ['restricted_admin', 'super_admin', 'lead_admin'])
    @pending = User.kept
      .where(invitation_accepted_at: nil)
      .where('invitation_sent_at >= ?', 90.days.ago)
      .order(Arel.sql('invitation_sent_at DESC NULLS LAST'))
    @archived = User.discarded.order(:first_name, :last_name, :email)
  end

  def primary_contacts
    if params[:non_primary].present?
      @non_primary_contacts = User.kept
        .where(primary_contact: nil)
        .where.not(invitation_accepted_at: nil)
        .order(:first_name, :last_name, :email)
    else
      @primary_contacts = User.kept
        .where.not(primary_contact: nil)
        .order(:primary_contact, :first_name, :last_name, :email)
    end
  end

  def remove_primary_contact
    if @user.update!(primary_contact: nil)
      render json: {
        user: @user,
        message: 'Primary contact successfully removed',
        status: :success
      }, status: :ok
    else
      render json: {
        errors: @user.errors.full_messages,
        message: 'Failed to remove primary contact',
        status: :error
      }, status: :unprocessable_entity
    end
  end

  def show
    render json: @user
  end

  def update
    if @user.update(user_params)
      render json: {
        user: @user,
        message: 'User successfully updated',
        status: :success
      }, status: :ok
    else
      render json: {
        errors: @user.errors.full_messages,
        message: 'Failed to update User',
        status: :error
      }, status: :unprocessable_entity
    end
  end

  def discard
    begin
      @user.discard
      @user.lock_access!
      @user.update!(role: 'user', primary_contact: nil)
      render json: { status: 'success', message: 'User successfully archived' }, status: :ok
    rescue => e
      render json: { status: 'error', message: e.message }, status: :unprocessable_entity
    end
  end

  def undiscard
    begin
      @user.undiscard
      @user.unlock_access!
      render json: { status: 'success', message: 'User successfully restored' }, status: :ok
    rescue => e
      render json: { status: 'error', message: e.message }, status: :unprocessable_entity
    end
  end


  def unlock
    @user.unlock_access!
  end

  private

  def user_params
    params.require(:user).permit(:role, :primary_contact)
  end
end
