class AlertsController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :alert
  before_action :set_motel, only: [:index, :show, :create, :edit, :update, :discard]
  before_action :set_alert, only: [:show, :edit, :update, :discard]

  # GET /alerts
  def index_all
    @open = Alert.open.sort_by(&:latest_history_at).reverse
    @closed = Alert.closed.order(closed_at: :desc)
    @archived = Alert.archived.sort_by(&:archived_at).reverse
    render 'alerts/index_all'
  end

  # GET /motels/:motel_id/alerts
  def index
    @alerts = @motel.alerts

    render json: @alerts
  end

  # GET /motels/:motel_id/alerts/:id
  def show
    render 'alerts/show'
  end

  # POST /motels/:motel_id/alerts
  def create
    @alert = @motel.alerts.new(alert_params)

    if params[:alert_status] === '1'
      @alert.closed_at = Time.current
      @alert.closed_user_id = current_user.id
    end
    @alert.user_id = current_user.id

    if @alert.save
      AlertLog.create!(
        user_id: current_user.id,
        alert_id: @alert.id,
        log_type: 'created',
        user_organization: @alert.user_organization,
        user_position_title: @alert.user_position_title
      )

      CreateNotificationJob.perform_later(:alert_created, current_user.id, alert: @alert)

      render json: @alert, status: :created
    else
      render json: @alert.errors, status: :unprocessable_entity
    end
  end

  def edit
    render 'alerts/edit'
  end

  def discard
    @alert.discard

    CreateNotificationJob.perform_later(:alert_discarded, current_user.id, alert: @alert)
  end

  # PATCH/PUT /motels/:motel_id/alerts/:id
  def update
    modified_params = assign_user_id_to_histories(alert_params, current_user.id)
    modified_params = remove_user_position_title_and_organization(modified_params)
    if params[:alert_status].to_s === '1'
      modified_params[:closed_at] = Time.current
      modified_params[:closed_user_id] = current_user.id
    end
    if @alert.update(modified_params)
      log_type = determine_log_type
      AlertLog.create!(
        user_id: current_user.id,
        alert_id: @alert.id,
        log_type: log_type,
        user_organization: @user_organization,
        user_position_title: @user_position_title
      )

      CreateNotificationJob.perform_later(:alert_updated, current_user.id, alert: @alert)

      render json: @alert, status: :ok
    else
      render json: @alert.errors, status: :unprocessable_entity
    end
  end

  private

  def set_motel
    @motel = Motel.find(params[:motel_id])
  end

  def set_alert
    @alert = @motel.alerts.find(params[:id])
  end

  def alert_params
    params.require(:alert).permit(
      :alert_type,
      :caution_issued,
      :closed_at,
      :closed_user_id,
      :closed_user_organization,
      :closed_user_position_title,
      :closure_note,
      :date,
      :description,
      :location,
      :reported_by,
      :time,
      :user_id,
      :user_organization,
      :user_position_title,
      witnesses: [],
      alert_actions_attributes: [
        :id,
        :name,
        :_destroy
      ],
      alert_histories_attributes: [
        :id,
        :user_id,
        :user_organization,
        :user_position_title,
        :description,
        :_destroy
      ]
    )
  end

  def assign_user_id_to_histories(params, user_id)
    return params unless params[:alert_histories_attributes]
    histories = params[:alert_histories_attributes]
    if histories
      histories.each do |history|
        history[:user_id] = user_id
      end
    end
    params
  end

  def remove_user_position_title_and_organization(params)
    # remove user_position_title and user_organization from alert params and assign them to user_position_title and user_organization variables for alert_log
    @user_position_title = params[:user_position_title]
    @user_organization = params[:user_organization]
    params.delete(:user_position_title)
    params.delete(:user_organization)
    params
  end

  def determine_log_type
    if params[:alert][:closed_at].present?
      'closed'
    elsif params[:alert][:alert_histories_attributes].present?
      'updated'
    elsif params[:alert][:alert_actions_attributes].present?
      'action_added'
    else
      'updated'
    end
  end
end
