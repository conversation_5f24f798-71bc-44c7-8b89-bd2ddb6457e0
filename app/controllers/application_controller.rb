class ApplicationController < ActionController::API
  include ActionController::Cookies
  include ActionController::MimeResponds
  before_action :configure_permitted_parameters, if: :devise_controller?

  respond_to :json

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.json do
        render json: {
          error: 'forbidden',
          message: 'Access denied'
        }, status: :forbidden
      end
    end
  end

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:invitation_token])
  end

  def current_user
    user = super

    if user.nil? && (data = cookies.encrypted[:mm_user]&.symbolize_keys)
      user = User.find_by(id: data[:user_id])
    end

    user
  end
end
