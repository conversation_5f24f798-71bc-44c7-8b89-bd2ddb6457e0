class AnnouncementsController < ApplicationController
  before_action :authenticate_user!

  load_and_authorize_resource

  def create
    @announcement.assign_attributes(announcement_params)
    @announcement.created_by = current_user

    if @announcement.save
      CreateNotificationJob.perform_later(:announcement_created, current_user.id, announcement: @announcement)
      render json: { status: 'success', message: 'Announcement sent' }
    else
      render json: @announcement.errors, status: :unprocessable_entity
    end
  end

  def show
    render json: @announcement
  end

  private

  def announcement_params
    params.require(:announcement).permit(:message)
  end
end
