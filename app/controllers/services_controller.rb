class ServicesController < ApplicationController
  before_action :authenticate_user!

  def index
    authorize! :read, Service
    other_types = OrganizationType.arel_table
    @organization_types = OrganizationType.all.order(
      other_types[:name].matches('%Other%').asc,
      :name
    )
  render 'services/index'
  end

  def new_form
    authorize! :create, Service
    other_types = OrganizationType.arel_table
    @organization_types = OrganizationType.all.order(
      other_types[:name].matches('%Other%').asc,
      :name
    )
    render 'services/new_form'
  end

  def create
    authorize! :create, Service
    @service = Service.new(service_params)
    if @service.save
      render json: @service, status: :created
    else
      render json: @service.errors, status: :unprocessable_entity
    end
  end

  private

  def service_params
    params.require(:service).permit(:name, :organization_type_id)
  end
end
