class CautionsController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :caution
  before_action :set_motel, only: [:index, :show, :create, :edit, :discard, :undiscard]
  before_action :set_caution, only: [:show, :edit, :discard, :undiscard]

  def create
    @caution = @motel.cautions.new(caution_params)
    @caution.user_id = current_user.id

    if @caution.save
      CreateNotificationJob.perform_later(:caution_created, current_user.id, caution: @caution)

      render json: @caution, status: :created
    else
      render json: @caution.errors, status: :unprocessable_entity
    end
  end

  def edit
    render 'cautions/edit'
  end

  def show
    render 'cautions/show'
  end

  def index
    @cautions = @motel.cautions.kept.order(created_at: :desc)
    render json: @cautions
  end

  def index_all
    @cautions = Caution.with_latest_timestamp
    render 'cautions/index_all'
  end

  def discard
    @caution.discard

    CreateNotificationJob.perform_later(:caution_discarded, current_user.id, caution: @caution)

    render json: { status: 'success', message: 'Caution archived' }
  end

  def undiscard
    @caution.undiscard

    CreateNotificationJob.perform_later(:caution_undiscarded, current_user.id, caution: @caution)

    render json: { status: 'success', message: 'Caution restored' }
  end

  private

  def set_motel
    @motel = Motel.find(params[:motel_id])
  end

  def set_caution
    @caution = @motel.cautions.find(params[:id])
  end

  def caution_params
    params.require(:caution).permit(
      :description,
      :authorised_by,
      :user_organization,
      :user_position_title,
    )
  end
end
