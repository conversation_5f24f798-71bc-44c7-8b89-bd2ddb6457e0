class MotelsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_motel, except: %i[index new_form create]
  include ActionController::Helpers
  helper <PERSON>telChangeLogHelper
  helper <PERSON>telHelper
  helper Am<PERSON><PERSON><PERSON><PERSON><PERSON>

  def index
    authorize! :read, Motel
    if params[:alert_form].present? || params[:caution_form].present? || params[:suggestion_form].present?
      @motels = Motel.all.order("LOWER(name) ASC").map { |motel| { value: motel.id, title: motel.name } }
      render json: @motels, status: :ok
    elsif params[:only_filter_fields].present?
      @motel_characteristics = Amenity.where(amenity_type: 'characteristic')
      @safety_requirements = Amenity.where(amenity_type: 'safety').order(name: :asc)
      @main_amenities = Amenity.where(amenity_type: 'amenity').order(name: :asc)
      @additional_amenities = Amenity.where(amenity_type: 'additional').where.not(name: 'Other Amenities').order(name: :asc)

      # TODO: Re-implement this when we refactor the amenities/amenity_options
      # @amenity_groups = Amenity
      #   .includes(:amenity_options)
      #   .where(amenity_options: { color: ['green', 'yellow'] })
      #   .order(:name)
      #   .where.not(name: 'Other Amenities')
      #   .group_by(&:amenity_type)

      other_types = OrganizationType.arel_table
      @organization_types = OrganizationType.order(
        other_types[:name].matches('%Other%').asc,
        :name
      )
    else
      filter_params = JSON.parse(params[:filter] || '{}')

      @motels, @counts = FilterMotelsQuery.new(
        filter_params: filter_params,
        page: params[:page],
        tab: params[:tab]
      ).call
    end
  end

  def new_form
    @motel = Motel.new
    authorize! :create, @motel

    load_form_data
    render 'motels/new'
  end

  def show
    authorize! :read, @motel

    @motel_characteristics = @motel.motel_amenities.joins(:amenity,
      :amenity_option).where(amenities: { amenity_type: 'characteristic' }).where.not(amenity_option: { name: [
:unknown, :no] })
    @safety_requirements = @motel.motel_amenities.joins(:amenity,
      :amenity_option).where(amenities: { amenity_type: 'safety' }).where.not(amenity_option: { name: 'Unknown' })
    @main_amenities = @motel.motel_amenities.joins(:amenity,
      :amenity_option).where(amenities: { amenity_type: 'amenity' }).where.not(amenity_option: { name: 'Unknown' })
    @additional_amenities = @motel.motel_amenities.joins(:amenity,
      :amenity_option).where(amenities: { amenity_type: 'additional' }).where.not(amenity_option: { name: 'Unknown' })
    @unknown = @motel.motel_amenities.joins(:amenity_option).where(amenity_options: { name: 'Unknown' })
  end

  def create
    if (source_motel_id = params[:source_motel_id])
      source_motel = Motel.find(source_motel_id)
      @motel = source_motel.duplicate!
      return render json: @motel, status: :created
    end

    ActiveRecord::Base.transaction do
      @motel = Motel.new(motel_params)
      authorize! :create, @motel

      if @motel.save
        update_motel_services(params.dig(:motel, :motel_service_ids), @motel.created_at)

        CreateNotificationJob.perform_later(:motel_created, current_user.id, motel: @motel)

        render json: @motel, status: :created, location: @motel
      else
        render json: @motel.errors, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::RecordInvalid => exception
    render json: { errors: exception.record.errors.full_messages }, status: :unprocessable_entity
  end

  def edit
    authorize! :update, @motel

    load_form_data
    render 'motels/edit'
  end

  def update
    ActiveRecord::Base.transaction do
      @motel.assign_attributes(motel_params)

      # This needs to be called before saving.
      logs = @motel.change_logs(current_user)

      if @motel.save
        performed_at = Time.current
        logs.each do |log|
          log.save_with_performed_at!(performed_at)
        end

        authorize! :update, @motel
        service_logs = update_motel_services(params.dig(:motel, :motel_service_ids), performed_at)

        if logs.size + service_logs.size > 0
          CreateNotificationJob.perform_later(:motel_updated, current_user.id, motel: @motel)
        end

        render json: @motel
      else
        render json: @motel.errors, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::RecordInvalid => exception
    raise exception
    render json: { errors: exception.record.errors.full_messages }, status: :unprocessable_entity
  end

  def inactivate
    if @motel.inactive?
      @motel.inactive = false
    else
      @motel.inactive = true
    end
    if @motel.save
      CreateNotificationJob.perform_later(
        @motel.inactive ? :motel_deactivated : :motel_reactivated, current_user.id, motel: @motel)

      render json: @motel, status: :ok
    else
      render json: @motel.errors, status: :unprocessable_entity
    end
  end

  # def destroy
  #   authorize! :destroy, @motel
  #   @motel.destroy
  # end

  def change_log
    authorize! :read, @motel
    @grouped_change_logs = @motel
      .motel_change_logs
      .includes(:user, :amenity, :amenity_option, :service)
      .order(performed_at: :desc)
      .group_and_sort_by_performed_at
  end

  private

  def set_motel
    @motel = Motel.find(params[:id])
  end

  private

  def load_form_data
    # Don't use DB order() because the display name is translated in Rails.
    @motel_characteristics = Amenity.where(amenity_type: 'characteristic').reject do |amenity|
      amenity.automatically_updated?
    end.sort_by(&:name)
    @safety_requirements = Amenity.where(amenity_type: 'safety').order(name: :asc)
    @main_amenities = Amenity.where(amenity_type: 'amenity').order(name: :asc)
    @additional_amenities = Amenity.where(amenity_type: 'additional').where.not(name: 'Other Amenities').order(name: :asc)

    @other = Amenity.find_by(name: 'Other Amenities')
    other_types = OrganizationType.arel_table
    @organization_types = OrganizationType.order(
      other_types[:name].matches('%Other%').asc,
      :name
    )
  end

  def motel_params
    params.require(:motel).permit(
      :name,
      :street,
      :postcode,
      :suburb,
      :state,
      :region,
      :density,
      :email,
      :phone,
      :website,
      :motel_type,
      duration: [],
      motel_services_attributes: [
        :id,
        :service_id,
        :_destroy
      ],
      motel_amenities_attributes: [
        :id,
        :note,
        :amenity_id,
        :amenity_option_id,
        :_destroy
      ]
    )
  end

  def update_motel_services(service_ids = [], motel_updated_at = nil)
    logs = []

    # Create new motel_services
    service_ids.map(&:to_i).uniq.each do |service_id|
      motel_service = @motel.motel_services.where(service_id: service_id).first_or_initialize

      logs << motel_service.change_log(current_user, :add) if motel_service.new_record? && motel_updated_at.present?

      unless motel_service.save
        @motel.errors.add(:base, "Service with ID #{service_id} could not be added.")
        raise ActiveRecord::RecordInvalid.new(@motel)
      end
    end

    # Skip if motel_updated_at is nil (i.e. when called from create action)
    return unless motel_updated_at.present?

    # Delete motel_services that are not in service_ids
    @motel.motel_services.where.not(service_id: service_ids).each do |motel_service|
      motel_service.destroy
      logs << motel_service.change_log(current_user, :remove)
    end

    logs.each do |log|
      log.save_with_performed_at!(motel_updated_at)
    end

    logs
  end
end
