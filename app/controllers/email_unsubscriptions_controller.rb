class EmailUnsubscriptionsController < ApplicationController
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def new
    @notification_preference = init_notification_preference
  end

  def create
    @notification_preference = init_notification_preference
    @notification_preference.update(email_unsubscription_params)
  end

  def index
    registry = current_user.notification_preferences.index_by(&:notification_event)

    @notification_groups = NotificationPreference::NOTIFICATION_GROUPS.transform_values do |events|
      events.map do |event|
        notification_config_class = "#{event}_notification_config".camelize.safe_constantize
        next unless notification_config_class&.new&.recipients&.where(id: current_user.id)&.exists?

        preference = registry[event.to_s] || current_user.notification_preferences.new(
          notification_event: event,
          email_notification_allowed: notification_config_class.new.default_email_recipient_roles.include?(current_user.role.to_sym)
        )

        { event: event, preference: preference }
      end.compact
    end
  end

  private

  def init_notification_preference
    unless (@unsubscribe_data = decrypt_email_unsubscription(params[:k]))
      raise ActionController::RoutingError, 'Invalid link'
    end

    @unsubscribe_data.deep_symbolize_keys!

    if (expired_at = @unsubscribe_data[:expired_at]&.to_datetime)
      raise ActionController::RoutingError, 'Expired' if expired_at < DateTime.current
    else
      raise ActionController::RoutingError, 'Invalid data'
    end

    user = User.find(@unsubscribe_data[:user_id])
    notification_event = @unsubscribe_data[:type]

    notification_preference = user.notification_preferences
                                  .where(notification_event: notification_event)
                                  .first_or_initialize(email_notification_allowed: true)

    notification_preference
  end

  private

  def email_unsubscription_params
    params.require(:email_unsubscription).permit(
      :email_notification_allowed,
    )
  end
end
