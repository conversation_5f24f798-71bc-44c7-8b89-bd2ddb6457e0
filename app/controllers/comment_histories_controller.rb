class CommentHistoriesController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :comment
  load_and_authorize_resource :comment_history, through: :comment

  def create
    @comment_history = @comment.comment_histories.new(comment_history_params)
    @comment_history.user_id = current_user.id
    @comment_history.comment_id = @comment.id
    if @comment_history.save
      CreateNotificationJob.perform_later(:comment_updated, current_user.id, comment: @comment)

      render json: @comment_history, status: :created
    else
      render json: @comment_history.errors, status: :unprocessable_entity
    end
  end

  private

  def comment_history_params
    params.require(:comment_history).permit(
      :body,
      :comment_id,
      :user_id,
      :user_position_title,
      :user_organization_title
    )
  end
end
