class SuggestionsController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :suggestion
  before_action :set_motel, only: [:index, :show, :create, :edit, :discard, :undiscard]
  before_action :set_suggestion, only: [:show, :edit, :discard, :undiscard]

  def index
    @suggestions = Suggestion.all
  end

  def show
  end

  def create
    @suggestion = @motel.suggestions.new(suggestion_params)
    @suggestion.user = current_user
    if @suggestion.save
      CreateNotificationJob.perform_later(:suggestion_created, current_user.id, suggestion: @suggestion)

      render json: @suggestion, status: :created
    else
      render json: @suggestion.errors, status: :unprocessable_entity
    end
  end


  def edit
    render 'suggestions/edit'
  end

  def index
    @suggestions = @motel.suggestions.kept.order(created_at: :desc)
    render json: @suggestions
  end

  def index_all
    @suggestions = Suggestion.with_latest_timestamp
    render 'suggestions/index_all'
  end

  def discard
    @suggestion.discard

    CreateNotificationJob.perform_later(:suggestion_discarded, current_user.id, suggestion: @suggestion)

    render json: { status: 'success', message: 'Suggestion archived' }
  end

  def undiscard
    @suggestion.undiscard

    CreateNotificationJob.perform_later(:suggestion_undiscarded, current_user.id, suggestion: @suggestion)

    render json: { status: 'success', message: 'Suggestion restored' }
  end

  private

  def set_suggestion
    @suggestion = Suggestion.find(params[:id])
  end

  def set_motel
    @motel = Motel.find(params[:motel_id])
  end

  def suggestion_params
    params.require(:suggestion).permit(
      :description,
      :user_organization,
      :user_position_title,
      :authorised_by,
      suggestion_client_cohorts_attributes: [
        :id,
        :client_cohort_id,
        :_destroy
      ],
    )
  end
end
