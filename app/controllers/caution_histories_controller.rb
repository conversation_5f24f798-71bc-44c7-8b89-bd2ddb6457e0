class CautionHistoriesController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :caution
  load_and_authorize_resource :caution_history, through: :caution

  def create
    @caution_history = @caution.caution_histories.new(caution_history_params)
    @caution_history.user_id = current_user.id
    @caution_history.caution_id = @caution.id
    if @caution_history.save
      CreateNotificationJob.perform_later(:caution_updated, current_user.id, caution: @caution)

      render json: @caution_history, status: :created
    else
      render json: @caution_history.errors, status: :unprocessable_entity
    end
  end

  private

  def caution_history_params
    params.require(:caution_history).permit(
      :description,
      :caution_id,
      :user_id,
      :user_position_title,
      :user_organization
    )
  end
end
