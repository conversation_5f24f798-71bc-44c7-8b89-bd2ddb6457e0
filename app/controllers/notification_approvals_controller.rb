class NotificationApprovalsController < ApplicationController
  before_action :authenticate_user!

  authorize_resource class: false

  def create
    approved_notification = Notification.review_required.find(params[:notification_id])

    event_group_key = approved_notification.event_group_key

    # When a lead admin approves a notification, also approve the relevant notifications of other lead admins
    Notification.review_required.where(event_group_key: event_group_key).find_each do |notification|
      notification.approved!
    end

    # Generate notifications for the rest of the users
    CreateNotificationJob.perform_later(
      approved_notification.notification_event,
      approved_notification.user.id,
      motel: approved_notification.motel,
      alert: approved_notification.alert,
      caution: approved_notification.caution,
      suggestion: approved_notification.suggestion,
      comment: approved_notification.comment,
      event_group_key: event_group_key
    )
  end
end
