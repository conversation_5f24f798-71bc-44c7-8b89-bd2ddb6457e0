class SuggestionHistoriesController < ApplicationController
  before_action :authenticate_user!
  load_and_authorize_resource :suggestion
  load_and_authorize_resource :suggestion_history, through: :suggestion

  def create
    @suggestion_history = @suggestion.suggestion_histories.new(suggestion_history_params)
    @suggestion_history.user_id = current_user.id
    @suggestion_history.suggestion_id = @suggestion.id
    if @suggestion_history.save
      CreateNotificationJob.perform_later(:suggestion_updated, current_user.id, suggestion: @suggestion)

      render json: @suggestion_history, status: :created
    else
      render json: @suggestion_history.errors, status: :unprocessable_entity
    end
  end

  private

  def suggestion_history_params
    params.require(:suggestion_history).permit(
      :description,
      :suggestion_id,
      :user_id,
      :user_position_title,
      :user_organization
    )
  end
end
