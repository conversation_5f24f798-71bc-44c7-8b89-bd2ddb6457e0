# Define the notifications as a collection-based resource with its 7 own RESTful actions.
# Inspired from https://stackoverflow.com/questions/7965949/best-practice-for-bulk-update-in-controller
# Guiding reference: https://ics.uci.edu/~fielding/pubs/dissertation/rest_arch_style.htm#sec_5_2_1_1
class NotificationBatchesController < ApplicationController
  before_action :authenticate_user!

  def update
    raise ActionController::BadRequest.new("Invalid ID") if params[:id] != 'current'

    case update_params[:batch_action]
    when 'approve'
      authorize! :create, :notification_approval
      process_notifications('approve')
    when 'reject'
      authorize! :create, :notification_rejection
      process_notifications('reject')
    when 'mark_as_read'
      mark_all_as_read
    else
      render json: { status: 'error', message: 'Invalid action' }, status: :unprocessable_entity
      return
    end

    render json: { status: 'success', message: @message }
  rescue => e
    render json: { status: 'error', message: "An error occurred while processing the request: #{e.message}" },
      status: :unprocessable_entity
  end

  private

  def update_params
    params.require(:notification_batch).permit(:id, :batch_action, notification_ids: [])
  end

  def process_notifications(action)
    BulkReviewNotificationsHandler.new(batch_action: action, params: update_params).call!
    @message = "Notifications #{action == 'approve' ? 'approved' : 'denied'} successfully"
  end

  def mark_all_as_read
    current_user.notifications.where(is_read: false).find_each do |notification|
      notification.update!(is_read: true)
    end
    @message = 'Notifications marked as read'
  end
end
