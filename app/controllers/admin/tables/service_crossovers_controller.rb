class Admin::Tables::ServiceCrossoversController < ApplicationController
  authorize_resource class: false

  helper <PERSON><PERSON><PERSON><PERSON><PERSON>

  def index
    @all_services = Service.all.order(:name)
    @all_organization_types = OrganizationType.all.order(:name)
    @all_motel_characteristics = Amenity.where(amenity_type: 'characteristic').order(:name)
    @sort_column = params[:sort_column]&.to_sym || :motel_name_asc

    if (service_id = params[:service_id])
      if service_id == 'all_motels'
        @motels = Motel.all
      else
        @selected_service = Service.find(service_id)
        @motels = @selected_service.motels
      end
      @motels = @motels.order(:name)

      if (organization_type_ids = params[:organization_type_ids] || params[:organization_type_ids_csv]&.split(','))
        @selected_organization_types = OrganizationType.where(id: organization_type_ids).order(:name)
      elsif (amenity_ids = params[:amenity_ids] || params[:amenity_ids_csv]&.split(','))
        @selected_amenities = Amenity.where(id: amenity_ids).sort_by(&:name)
      end
    end
  end
end
