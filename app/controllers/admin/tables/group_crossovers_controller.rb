class Admin::Tables::GroupCrossoversController < ApplicationController
  authorize_resource class: false

  helper <PERSON><PERSON><PERSON><PERSON><PERSON>

  def index
    @crossover_type = params[:crossover_type]&.to_sym

    @all_organization_types = OrganizationType.all.order(:name)
    @all_motel_characteristics = Amenity.where(amenity_type: 'characteristic').order(:name)

    @motels = Motel.all.where.not(motel_type: ['Rooming House', 'Rooming House - Unregistered']).order(:name)

    group1_organization_type_ids = params[:group1_organization_type_ids] || params[:group1_organization_type_ids_csv]&.split(',')
    @group1_selected_organization_types = OrganizationType.where(id: group1_organization_type_ids).order(:name)

    group1_amenity_ids = params[:group1_amenity_ids] || params[:group1_amenity_ids_csv]&.split(',')
    @group1_selected_amenities = Amenity.where(id: group1_amenity_ids).sort_by(&:name)

    group2_organization_type_ids = params[:group2_organization_type_ids] || params[:group2_organization_type_ids_csv]&.split(',')
    @group2_selected_organization_types = OrganizationType.where(id: group2_organization_type_ids).order(:name)

    group2_amenity_ids = params[:group2_amenity_ids] || params[:group2_amenity_ids_csv]&.split(',')
    @group2_selected_amenities = Amenity.where(id: group2_amenity_ids).sort_by(&:name)
  end
end
