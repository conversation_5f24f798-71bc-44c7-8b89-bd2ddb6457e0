class Admin::Tables::CommentsController < ApplicationController
  authorize_resource class: :admin_comment

  def index
    # No pagination for the time being
    @review_required_comments = Comment.review_required.created_desc.page(1).per(50)
    @approved_comments = Comment.approved.created_desc.page(1).per(50)
    @rejected_comments = Comment.rejected.created_desc.page(1).per(50)
  end

  def show
    @comment = Comment.find(params[:id])
  end

  def update
    @update_mode = params[:mode]&.to_sym
    @comment = Comment.find(params[:id])

    approving = false

    case @update_mode
    when :approve
      # Prevent duplicate notification in the event of double submission.
      if !@comment.approved?
        @comment.active_status = :approved
        @comment.approved_at = DateTime.current

        @comment.rejected_at = nil
        @comment.rejection_reason = nil
        @comment.rejection_note = nil

        approving = true
      end
    when :reject
      @comment.assign_attributes(comment_params)
      @comment.active_status = :rejected
      @comment.rejected_at = DateTime.current
      @comment.approved_at = nil
    else
      raise "Invalid mode: #{@update_mode}"
    end

    if @comment.save
      if approving
        CreateNotificationJob.perform_later(:comment_created, current_user.id, comment: @comment)
      end

      render json: @comment, status: :ok
    else
      render json: @comment.errors, status: :unprocessable_entity
    end
  end

  private

  def comment_params
    params.require(:comment).permit(
      :rejection_reason,
      :rejection_note
    )
  end
end
