class BulkReviewNotificationsHandler
  attr_reader :batch_action, :notification_ids

  def initialize(batch_action:, params:)
    @batch_action = batch_action
    @notification_ids = params[:notification_ids]
    @approved_notifications = []
  end

  def call!
    raise ArgumentError, "Invalid parameters" if invalid_params?

    notifications = Notification.review_required.where(id: notification_ids)
    raise ActiveRecord::RecordNotFound, "No notifications found" if notifications.empty?

    process_notifications(notifications)
    schedule_notification_jobs
  end

  private

  def invalid_params?
    notification_ids.blank? || !%w[approve reject].include?(batch_action)
  end

  def process_notifications(notifications)
    Notification.transaction do
      notifications.each do |notification|
        event_group_key = notification.event_group_key
        related_notifications = Notification.review_required.where(event_group_key: event_group_key)

        related_notifications.find_each do |n|
          if batch_action == 'approve'
            n.approved!
            @approved_notifications << n
          else
            n.rejected!
          end
        end
      end
    end
  end

  def schedule_notification_jobs
    @approved_notifications.each do |notification|
      CreateNotificationJob.perform_later(
        notification.notification_event,
        notification.user.id,
        motel: notification.motel,
        alert: notification.alert,
        caution: notification.caution,
        suggestion: notification.suggestion,
        comment: notification.comment,
        event_group_key: notification.event_group_key
      )
    end
  end
end
