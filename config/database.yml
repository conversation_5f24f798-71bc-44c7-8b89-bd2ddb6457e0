default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  host: <%= ENV['DATABASE_HOST'] || 'localhost' %>
  port: <%= ENV['DATABASE_PORT'] || 5432 %>
  username: <%= ENV['DATABASE_USERNAME'] || 'postgres' %>
  password: <%= ENV['DATABASE_PASSWORD'] || '' %>

development:
  <<: *default
  database: motel_coordination_development

test:
  <<: *default
  database: motel_coordination_test

production:
  <<: *default
  url: <%= ENV.fetch("DATABASE_URL") {} %>

staging:
  <<: *default
  url: <%= ENV.fetch("DATABASE_URL") {} %>
