# frozen_string_literal: true

# Preview all emails at http://localhost:3000/rails/mailers
Rails.application.routes.draw do
  # A fake route that allows us to use `root_url` as a base host for frontend routes.
  root 'home#index'

  get 'current_user', to: 'current_user#index'

  devise_for :users, path: '', path_names: {
    sign_in: 'login',
    sign_out: 'logout',
    registration: 'signup'
  },
  controllers: {
    sessions: 'users/sessions',
    passwords: 'users/passwords',
    invitations: 'users/invitations'
  }

  devise_scope :user do
    post '/users/:id/resend_invitation', to: 'users/invitations#resend_invitation'
    post '/users/:id/cancel_invitation', to: 'users/invitations#cancel_invitation'
  end

  namespace :admin do
    # resources :stats, only: [] do
    #   collection do
    #     authenticated :user, ->(u) { u.super_admin? } do
    #       mount Sidekiq::Web => '/sidekiq'
    #     end
    #   end
    # end
    #

    namespace :tables do
      resources :comments, only: [:index, :show, :update]
      resources :service_crossovers, only: [:index]
      resources :group_crossovers, only: [:index]
    end
  end

  resources :users, only: [:index, :show, :update] do
    collection do
      get :primary_contacts
    end
    member do
      patch :remove_primary_contact
      patch :discard
      patch :undiscard
      patch :unlock
    end
  end

  get 'admin_settings', to: 'admin_settings#show'

  get '/alerts', to: 'alerts#index_all'
  get '/cautions', to: 'cautions#index_all'
  get '/suggestions', to: 'suggestions#index_all'

  resources :alert_logs, only: [:index]
  resources :client_cohorts
  resources :services, only: [:index, :create] do
    collection do
      get :new_form
    end
  end

  resources :motels do
    resources :alerts, only: [:index, :show, :edit, :create, :update] do
      resources :alert_logs, only: [:create]
      resources :alert_histories, only: [:create]
      resources :alert_actions, only: [:create, :update]
      member do
        patch :discard
      end
    end
    resources :cautions, only: [:create, :edit, :show, :index] do
      resources :caution_histories, only: [:create]
      member do
        patch :discard
        patch :undiscard
      end
    end
    resources :suggestions, only: [:create, :edit, :show, :index] do
      resources :suggestion_histories, only: [:create]
      member do
        patch :discard
        patch :undiscard
      end
    end
    resources :comments, only: [:create, :update, :index] do
      resources :comment_histories, only: [:create]
      member do
        patch :pin
        patch :archive
      end
    end
    collection do
      get :new_form
    end
    member do
      get :edit
      patch :inactivate
      get :change_log
    end
  end

  resources :notifications, only: [:index, :show]
  resources :notification_batches, only: [:update]
  resources :notification_preference_batches, only: [:update]
  resources :notification_approvals, only: [:create]
  resources :notification_rejections, only: [:create]
  resources :announcements, only: [:create, :show]

  resources :email_unsubscriptions, only: [:index, :new, :create]
end
