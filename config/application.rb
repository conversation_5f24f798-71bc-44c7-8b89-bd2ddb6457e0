require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module MotelCoordinationApi
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    config.time_zone = 'Australia/Melbourne'
    # config.eager_load_paths << Rails.root.join("extras")

    # Add the app/queries directory to autoload paths
    config.autoload_paths += %W(#{config.root}/app/queries)

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.

    config.middleware.use ActionDispatch::Cookies
    # config.middleware.use ActionDispatch::Session::CookieStore, config.session_options
    # config.session_store :cookie_store, key: '_motel_coordination_api_session'

    config.api_only = true

    ### Custom

    # These have to be set here because they get used in some initializers,
    # such as `devise.rb``
    host = 'lvh.me'
    config.mailer_host = ENV.fetch('MAILER_HOST') { host }
    config.web_host = ENV.fetch('WEB_HOST') { "www.#{config.mailer_host}" }

    ###
  end
end
