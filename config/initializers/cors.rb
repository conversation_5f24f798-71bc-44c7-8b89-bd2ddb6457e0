# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin AJAX requests.

# Read more: https://github.com/cyu/rack-cors

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    if Rails.env.production?
      origins 'https://motel-coordination-frontend.herokuapp.com', 'https://www.motel-coordination-frontend.herokuapp.com', 'https://motel-mapping.net', 'https://www.motel-mapping.net'
    elsif Rails.env.staging?
      origins 'https://motel-coordination-staging.herokuapp.com'
    else
      origins 'http://localhost:5173', 'http://www.lvh.me:5173'
    end

    resource "/login",
      headers: :any,
      expose: ["Authorization"],
      credentials: true,
      methods: [:get, :post, :put, :patch, :delete, :options, :head]

    resource "*",
      headers: :any,
      expose: ["Authorization"],
      methods: [:get, :post, :put, :patch, :delete, :options, :head]
  end
end
