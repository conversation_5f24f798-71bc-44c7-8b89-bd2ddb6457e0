# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

AdminSetting.first_or_create!

# Create all amenities and their amenity_options
safety = [
  {
    name: 'CCTV',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'CCTV', icon: 'photo_camera_front', color: 'green' },
      { name: 'No', display_text: 'No CCTV', icon: 'photo_camera_front', color: 'red' },
      { name: 'Unknown', display_text: 'CCTV', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Staffed Reception',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes (24h)', display_text: '24h Staffed Reception', icon: 'history_toggle_off', color: 'green' },
      { name: 'Yes (Limited Hours)', display_text: 'Staffed Reception', icon: 'history_toggle_off', color: 'yellow' },
      { name: 'No', display_text: 'No Staffed Reception', icon: 'history_toggle_off', color: 'red' },
      { name: 'Unknown', display_text: 'Staffed Reception', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Safe Room Access',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'Safe Room Access', icon: 'login', color: 'green' },
      { name: 'No', display_text: 'Unsafe Room Access', icon: 'login', color: 'red' },
      { name: 'Unknown', display_text: 'Room Access', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'In-Room Phone',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'In-Room Phone', icon: 'phone', color: 'green' },
      { name: 'No', display_text: 'No In-Room Phone', icon: 'phone', color: 'red' },
      { name: 'Unknown', display_text: 'In-Room Phone', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Keyless Door Entry',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'Keyless Door Entry', icon: 'key_off', color: 'green' },
      { name: 'No', display_text: 'No Keyless Door Entry', icon: 'key_off', color: 'red' },
      { name: 'Unknown', display_text: 'Keyless Door Entry', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Out-of-Hours Check-in',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'Out-of-Hours Check-in', icon: 'nights_stay', color: 'green' },
      { name: 'No', display_text: 'No Out-of-Hours Check-in', icon: 'nights_stay', color: 'red' },
      { name: 'Unknown', display_text: 'Out-of-Hours Check-in', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Manager Available After Hours',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes (24h)', display_text: 'Manager Available 24h', icon: 'person_pin', color: 'green' },
      { name: 'Yes (Limited Hours)', display_text: 'Manager Available After Hours', icon: 'person_pin', color: 'yellow' },
      { name: 'No', display_text: 'No Manager Available After Hours', icon: 'person_pin', color: 'red' },
      { name: 'Unknown', display_text: 'Manager Available After Hours', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Out-of-Hours Booking',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes (24h)', display_text: '24h Booking', icon: 'update', color: 'green' },
      { name: 'Yes (Limited Hours)', display_text: 'Out-of-Hours Booking', icon: 'update', color: 'yellow' },
      { name: 'No', display_text: 'No Out-of-Hours Booking', icon: 'update', color: 'red' },
      { name: 'Unknown', display_text: 'Out-of-Hours Booking', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Accepts Single Males',
    amenity_type: 'safety',
    amenity_options: [
      { name: 'Yes', display_text: 'Accepts Single Male Clients', icon: 'man', color: 'red' },
      { name: 'No', display_text: 'No Single Male Clients Accepted', icon: 'man', color: 'green' },
      { name: 'Unknown', display_text: 'Accepts Single Male Clients', icon: 'help', color: 'gray' }
    ]
  },
]

amenity = [
  {
    name: '3 or More Bedrooms Available',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: '3+ Bdr Available', icon: 'groups', color: 'green' },
      { name: 'No', display_text: '3+ Bdr Not Available', icon: 'groups', color: 'red' },
      { name: 'Unknown', display_text: '3+ Bdr Available', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Wheelchair Accessibility',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Wheelchair Accessibility', icon: 'accessible', color: 'green' },
      { name: 'No', display_text: 'No Wheelchair Accessibility', icon: 'accessible', color: 'red' },
      { name: 'Unknown', display_text: 'Wheelchair Accessibility', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Kitchen (Private)',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Kitchen', display_text: 'Kitchen (Private)', icon: 'restaurant', color: 'green' },
      { name: 'Kitchenette', display_text: 'Kitchenette', icon: 'restaurant', color: 'yellow' },
      { name: 'No', display_text: 'No Kitchen', icon: 'restaurant', color: 'red' },
      { name: 'Unknown', display_text: 'Kitchen', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Laundry',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes (Free)', display_text: 'Laundry (Free)', icon: 'local_laundry_service', color: 'green' },
      { name: 'Yes (Paid)', display_text: 'Laundry (Paid)', icon: 'local_laundry_service', color: 'yellow' },
      { name: 'No', display_text: 'No Laundry', icon: 'local_laundry_service', color: 'red' },
      { name: 'Unknown', display_text: 'Laundry', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Private Balcony',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Private Balcony', icon: 'balcony', color: 'green' },
      { name: 'No', display_text: 'No Private Balcony', icon: 'balcony', color: 'red' },
      { name: 'Unknown', display_text: 'Private Balcony', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Private Courtyard',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Private Courtyard', icon: 'deck', color: 'green' },
      { name: 'No', display_text: 'No Private Courtyard', icon: 'deck', color: 'red' },
      { name: 'Unknown', display_text: 'Private Courtyard', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Smoking Area',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes (Private)', display_text: 'Private Smoking Area', icon: 'smoking_rooms', color: 'green' },
      { name: 'Yes (Communal Only)', display_text: 'Communal Smoking Area', icon: 'smoking_rooms', color: 'yellow' },
      { name: 'No', display_text: 'No Smoking Area', icon: 'smoking_rooms', color: 'red' },
      { name: 'Unknown', display_text: 'Smoking Area', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Bathtub',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Bathtub', icon: 'bathtub', color: 'green' },
      { name: 'No', display_text: 'No Bathtub', icon: 'bathtub', color: 'red' },
      { name: 'Unknown', display_text: 'Bathtub', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Cot',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Cot', icon: 'crib', color: 'green' },
      { name: 'No', display_text: 'No Cot', icon: 'crib', color: 'red' },
      { name: 'Unknown', display_text: 'Cot', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Rollaway Bed',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes', display_text: 'Rollaway Bed', icon: 'airline_seat_flat', color: 'green' },
      { name: 'No', display_text: 'No Rollaway Bed', icon: 'airline_seat_flat', color: 'red' },
      { name: 'Unknown', display_text: 'Rollaway Bed', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Accept Invoices',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'Yes (Always)', display_text: 'Invoice Payment', icon: 'local_atm', color: 'green' },
      { name: 'Yes (Sometimes)', display_text: 'Invoice Payment (Sometimes)', icon: 'local_atm', color: 'yellow' },
      { name: 'No', display_text: 'Credit Card Payment', icon: 'local_atm', color: 'red' },
      { name: 'Unknown', display_text: 'Accepted Payment Type', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'ID Required',
    amenity_type: 'amenity',
    amenity_options: [
      { name: 'No', display_text: 'No ID Required', icon: 'verified_user', color: 'green' },
      { name: 'Yes (Sometimes)', display_text: 'ID Required (Sometimes)', icon: 'verified_user', color: 'yellow' },
      { name: 'Yes (Always)', display_text: 'ID Required (Always)', icon: 'verified_user', color: 'red' },
      { name: 'Unknown', display_text: 'ID Required', icon: 'help', color: 'gray' }
    ]
  },
]

additional = [
  {
    name: 'Pet Friendly',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes (All Pets)', display_text: 'All Pets Allowed', icon: 'pets', color: 'green' },
      { name: 'Yes (Some Pets)', display_text: 'Some Pets Allowed', icon: 'pets', color: 'yellow' },
      { name: 'No', display_text: 'No Pets Allowed', icon: 'pets', color: 'red' },
      { name: 'Unknown', display_text: 'Pets Allowed', icon: 'help', color: 'gray' }
    ],
  },
  {
    name: 'Wifi/Internet',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes (Free)', display_text: 'Free Wifi/Internet', icon: 'wifi', color: 'green' },
      { name: 'Yes (Paid)', display_text: 'Wifi/Internet', icon: 'wifi', color: 'yellow' },
      { name: 'No', display_text: 'No Wifi/Internet', icon: 'wifi', color: 'red' },
      { name: 'Unknown', display_text: 'Wifi/Internet', icon: 'help', color: 'gray' }
    ],
  },
  {
    name: 'On-Site Parking',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes (Free)', display_text: 'Free On-Site Parking', icon: 'local_parking', color: 'green' },
      { name: 'Yes (Paid)', display_text: 'Paid On-Site Parking', icon: 'local_parking', color: 'yellow' },
      { name: 'No', display_text: 'No On-Site Parking', icon: 'local_parking', color: 'red' },
      { name: 'Unknown', display_text: 'On-Site Parking', icon: 'help', color: 'gray' }
    ],
  },
  {
    name: 'In-Room Safe',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes', display_text: 'In-Room Safe', icon: 'lock', color: 'green' },
      { name: 'No', display_text: 'No In-Room Safe', icon: 'lock', color: 'red' },
      { name: 'Unknown', display_text: 'In-Room Safe', icon: 'help', color: 'gray' }
    ],
  },
  {
    name: 'Luggage Storage',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes', display_text: 'Luggage Storage', icon: 'luggage', color: 'green' },
      { name: 'No', display_text: 'No Luggage Storage', icon: 'luggage', color: 'red' },
      { name: 'Unknown', display_text: 'Luggage Storage', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Voucher Allocation',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes', display_text: 'Voucher Allocation', icon: 'card_giftcard', color: 'green' },
      { name: 'No', display_text: 'No Voucher Allocation', icon: 'card_giftcard', color: 'red' },
      { name: 'Unknown', display_text: 'Voucher Allocation', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Other Accessibility Options',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes', display_text: 'Other Accessibility Options', icon: 'elderly', color: 'green' },
      { name: 'No', display_text: 'No Other Accessibility Options', icon: 'elderly', color: 'red' },
      { name: 'Unknown', display_text: 'Other Accessibility Options', icon: 'help', color: 'gray' }
    ]
  },
  {
    name: 'Other Amenities',
    amenity_type: 'additional',
    amenity_options: [
      { name: 'Yes', display_text: 'Other', icon: 'add_circle', color: 'green' },
      { name: 'No', display_text: 'No Other Amenities', icon: 'add_circle', color: 'red' }
    ]
  }
]

all_features = safety + amenity + additional

all_features.each do |feature|
  amenity = Amenity.find_or_create_by!(name: feature[:name], amenity_type: feature[:amenity_type])

  feature[:amenity_options].each do |option|
    AmenityOption.find_or_create_by!(
      name: option[:name],
      display_text: option[:display_text],
      icon: option[:icon],
      color: option[:color],
      amenity_id: amenity.id
    )
  end
end

if Rails.env.development?
  exit if Motel.all.size >= 100

  User.create!(
    first_name: 'Lead',
    last_name: 'Admin',
    email: '<EMAIL>',
    password: 'password',
    role: :lead_admin,
    invitation_accepted_at: DateTime.current
  )
  User.create!(
    first_name: 'Super',
    last_name: 'Admin',
    email: '<EMAIL>',
    password: 'password',
    role: :super_admin,
    invitation_accepted_at: DateTime.current
  )
  User.create!(
    first_name: 'Restricted',
    last_name: 'Admin',
    email: '<EMAIL>',
    password: 'password',
    role: :restricted_admin,
    invitation_accepted_at: DateTime.current
  )

  # User.create!(first_name: 'First', last_name: 'Staff', email: '<EMAIL>', password: 'password')
  # User.create!(first_name: 'Second', last_name: 'Staff', email: '<EMAIL>', password: 'password')

  require 'faker'
  # Load the Rake environment
  Rails.application.load_tasks

  # Execute the Rake tasks
  # Create all the predefined organization types
  Rake::Task['organization_types:add_predefined'].invoke
  # Add help text to amenities
  Rake::Task['amenities:add_help_text'].invoke

  Rake::Task['amenities:create_motel_characteristic_amenities'].invoke
  Rake::Task['populate:client_cohorts'].invoke
  Rake::Task['services:create_services'].invoke
  Rake::Task['motels:assign_services'].invoke

  # Constant variables
  DENSITIES = [
    "Low",
    "Medium",
    "High"
  ]

  DURATIONS = [
    'Single Night Stay',
    'Short Term Stay',
    'Long Term Stay'
  ]

  REGIONS = [
    "Barwon",
    "Bayside Peninsula",
    "Brimbank Melton",
    "Central Highlands",
    "Goulburn",
    "Hume Merri-Bek",
    "Outer-Eastern Melbourne",
    "Inner-Eastern Melbourne",
    "Inner-Gippsland",
    "Loddon",
    "Melbourne CBD",
    "North-Eastern Melbourne",
    "Outer-Gippsland",
    "Southern Melbourne",
    "Western Melbourne",
    "Outer Western District",
    "Mallee",
    "Ovens Murray"
  ]

  MOTEL_TYPES = [
    "Hotel",
    "Apartment Hotel",
    "Caravan Park",
    "Homestay",
    "Hostel",
    "Motel/Motor Inn",
    "Rooming House",
    "Rooming House - Unregistered",
    "Serviced Apartments",
    "Single Unit",
    "SRS (Pension Level)",
    "SRS (Above Pension Level)"
  ]

  # Define a function to generate a random motel name based on Faker's company name
  def generate_motel_name
    Faker::Company.name
  end

  # Define a function to generate a random email address based on the motel name
  def generate_email(name)
    email_domain = Faker::Internet.domain_name
    email_username = name&.gsub(/\s+/, "")&.downcase || "motel"
    "#{email_username}@#{email_domain}"
  end

  # Define a function to generate a random website URL based on the motel name
  def generate_website(name)
    website_domain = Faker::Internet.domain_name
    website_slug = name&.gsub(/\s+/, "-")&.downcase || "motel"
    "http://www.#{website_slug}.#{website_domain}"
  end

  # Fetch all amenities, amenity options, and organization types
  amenities = Amenity.all
  amenity_options = AmenityOption.all
  organization_types = OrganizationType.all

  # Random notes array, add more notes as needed
  notes = ["This is a note.", "Test Note.", nil]

  # Create motels
  100.times do
    motel_name = generate_motel_name
    motel = Motel.create(
      name: motel_name,
      density: DENSITIES.sample,
      duration: DURATIONS.sample(rand(1..3)),
      email: generate_email(motel_name),
      motel_type: MOTEL_TYPES.sample,
      phone: Faker::PhoneNumber.phone_number,
      postcode: Faker::Number.between(from: 3000, to: 3999),
      region: REGIONS.sample,
      state: 'VIC',
      street: Faker::Address.street_address,
      suburb: Faker::Address.city,
      website: generate_website(motel_name),
      motel_amenities_attributes: amenities.map do |amenity|
        amenity_option = amenity.amenity_options.sample
        note = amenity_option.color == 'gray' ? nil : notes.sample
        {
          amenity: amenity,
          amenity_option: amenity_option,
          note: note
        }
      end
    )
  end
end
