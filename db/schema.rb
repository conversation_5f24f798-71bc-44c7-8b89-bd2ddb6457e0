# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2024_10_05_074124) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "admin_settings", force: :cascade do |t|
    t.string "reset_passcode"
    t.datetime "reset_passcode_updated_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "alert_actions", force: :cascade do |t|
    t.bigint "alert_id", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_id"], name: "index_alert_actions_on_alert_id"
  end

  create_table "alert_histories", force: :cascade do |t|
    t.bigint "alert_id", null: false
    t.bigint "user_id", null: false
    t.string "user_position_title"
    t.string "user_organization"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_id"], name: "index_alert_histories_on_alert_id"
    t.index ["user_id"], name: "index_alert_histories_on_user_id"
  end

  create_table "alert_logs", force: :cascade do |t|
    t.bigint "alert_id", null: false
    t.bigint "user_id", null: false
    t.string "user_position_title"
    t.string "user_organization"
    t.string "log_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_id"], name: "index_alert_logs_on_alert_id"
    t.index ["user_id"], name: "index_alert_logs_on_user_id"
  end

  create_table "alerts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "motel_id", null: false
    t.string "user_position_title"
    t.string "user_organization"
    t.string "alert_type"
    t.date "date"
    t.time "time"
    t.text "location"
    t.string "witnesses", default: [], array: true
    t.text "description"
    t.datetime "closed_at"
    t.string "closure_note"
    t.string "reported_by"
    t.boolean "caution_issued", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.string "closed_user_position_title"
    t.string "closed_user_organization"
    t.bigint "closed_user_id"
    t.index ["closed_user_id"], name: "index_alerts_on_closed_user_id"
    t.index ["discarded_at"], name: "index_alerts_on_discarded_at"
    t.index ["motel_id"], name: "index_alerts_on_motel_id"
    t.index ["user_id"], name: "index_alerts_on_user_id"
  end

  create_table "amenities", force: :cascade do |t|
    t.string "name", null: false
    t.string "amenity_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "help_text"
  end

  create_table "amenity_options", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "amenity_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "display_text", null: false
    t.string "icon", null: false
    t.string "color", null: false
    t.index ["amenity_id"], name: "index_amenity_options_on_amenity_id"
  end

  create_table "announcements", force: :cascade do |t|
    t.bigint "created_by_id", null: false
    t.text "message", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_announcements_on_created_by_id"
  end

  create_table "caution_histories", force: :cascade do |t|
    t.text "description"
    t.string "user_position_title"
    t.string "user_organization"
    t.bigint "caution_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["caution_id"], name: "index_caution_histories_on_caution_id"
    t.index ["user_id"], name: "index_caution_histories_on_user_id"
  end

  create_table "cautions", force: :cascade do |t|
    t.text "description"
    t.datetime "discarded_at"
    t.string "authorised_by"
    t.string "user_organization"
    t.string "user_position_title"
    t.bigint "motel_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["motel_id"], name: "index_cautions_on_motel_id"
    t.index ["user_id"], name: "index_cautions_on_user_id"
  end

  create_table "client_cohorts", force: :cascade do |t|
    t.string "name"
    t.boolean "red"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "comment_histories", force: :cascade do |t|
    t.text "body"
    t.bigint "comment_id", null: false
    t.bigint "user_id", null: false
    t.string "user_position_title", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "user_organization_title", null: false
    t.index ["comment_id"], name: "index_comment_histories_on_comment_id"
    t.index ["user_id"], name: "index_comment_histories_on_user_id"
  end

  create_table "comments", force: :cascade do |t|
    t.text "body"
    t.boolean "pinned", default: false
    t.boolean "archived", default: false
    t.bigint "user_id", null: false
    t.string "user_position_title", null: false
    t.bigint "motel_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "user_organization_title", default: "", null: false
    t.integer "active_status"
    t.datetime "approved_at"
    t.datetime "rejected_at"
    t.integer "rejection_reason"
    t.text "rejection_note"
    t.index ["motel_id"], name: "index_comments_on_motel_id"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "motel_amenities", force: :cascade do |t|
    t.bigint "motel_id", null: false
    t.bigint "amenity_id", null: false
    t.bigint "amenity_option_id", null: false
    t.text "note"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["amenity_id"], name: "index_motel_amenities_on_amenity_id"
    t.index ["amenity_option_id"], name: "index_motel_amenities_on_amenity_option_id"
    t.index ["motel_id"], name: "index_motel_amenities_on_motel_id"
  end

  create_table "motel_change_logs", force: :cascade do |t|
    t.bigint "motel_id", null: false
    t.bigint "user_id", null: false
    t.bigint "amenity_id"
    t.bigint "amenity_option_id"
    t.bigint "service_id"
    t.string "column_name"
    t.string "column_value"
    t.integer "activity_type"
    t.datetime "performed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "amenity_note"
    t.index ["amenity_id"], name: "index_motel_change_logs_on_amenity_id"
    t.index ["amenity_option_id"], name: "index_motel_change_logs_on_amenity_option_id"
    t.index ["motel_id"], name: "index_motel_change_logs_on_motel_id"
    t.index ["service_id"], name: "index_motel_change_logs_on_service_id"
    t.index ["user_id"], name: "index_motel_change_logs_on_user_id"
  end

  create_table "motel_services", force: :cascade do |t|
    t.bigint "motel_id", null: false
    t.bigint "service_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["motel_id", "service_id"], name: "index_motel_services_on_motel_id_and_service_id", unique: true
    t.index ["motel_id"], name: "index_motel_services_on_motel_id"
    t.index ["service_id"], name: "index_motel_services_on_service_id"
  end

  create_table "motels", force: :cascade do |t|
    t.string "name"
    t.string "region"
    t.string "street"
    t.string "suburb"
    t.integer "postcode"
    t.string "density"
    t.string "phone"
    t.string "email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "motel_type"
    t.string "state", default: "VIC"
    t.string "duration", default: [], array: true
    t.string "website"
    t.boolean "inactive"
  end

  create_table "notification_preferences", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "notification_event"
    t.boolean "email_notification_allowed"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "notification_event"], name: "index_notification_preferences_on_user_id_and_event", unique: true
    t.index ["user_id"], name: "index_notification_preferences_on_user_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "motel_id"
    t.bigint "alert_id"
    t.bigint "caution_id"
    t.bigint "suggestion_id"
    t.bigint "comment_id"
    t.boolean "is_read"
    t.integer "notification_event"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "approval_status"
    t.uuid "event_group_key"
    t.bigint "announcement_id"
    t.index ["alert_id"], name: "index_notifications_on_alert_id"
    t.index ["announcement_id"], name: "index_notifications_on_announcement_id"
    t.index ["caution_id"], name: "index_notifications_on_caution_id"
    t.index ["comment_id"], name: "index_notifications_on_comment_id"
    t.index ["event_group_key", "user_id"], name: "index_notifications_on_event_group_key_and_user_id", unique: true
    t.index ["motel_id"], name: "index_notifications_on_motel_id"
    t.index ["suggestion_id"], name: "index_notifications_on_suggestion_id"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "organization_types", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "background_color"
    t.string "color"
  end

  create_table "services", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "organization_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_type_id"], name: "index_services_on_organization_type_id"
  end

  create_table "suggestion_client_cohorts", force: :cascade do |t|
    t.bigint "suggestion_id", null: false
    t.bigint "client_cohort_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["client_cohort_id"], name: "index_suggestion_client_cohorts_on_client_cohort_id"
    t.index ["suggestion_id"], name: "index_suggestion_client_cohorts_on_suggestion_id"
  end

  create_table "suggestion_histories", force: :cascade do |t|
    t.bigint "suggestion_id", null: false
    t.bigint "user_id", null: false
    t.text "description"
    t.string "user_organization"
    t.string "user_position_title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["suggestion_id"], name: "index_suggestion_histories_on_suggestion_id"
    t.index ["user_id"], name: "index_suggestion_histories_on_user_id"
  end

  create_table "suggestions", force: :cascade do |t|
    t.bigint "motel_id", null: false
    t.bigint "user_id", null: false
    t.text "description"
    t.string "authorised_by"
    t.string "user_organization"
    t.string "user_position_title"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["motel_id"], name: "index_suggestions_on_motel_id"
    t.index ["user_id"], name: "index_suggestions_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "jti", null: false
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "locked_at"
    t.integer "role", default: 0, null: false
    t.string "first_name"
    t.string "last_name"
    t.datetime "discarded_at"
    t.string "primary_contact"
    t.datetime "password_changed_at"
    t.index ["discarded_at"], name: "index_users_on_discarded_at"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by"
    t.index ["jti"], name: "index_users_on_jti", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "alert_actions", "alerts"
  add_foreign_key "alert_histories", "alerts"
  add_foreign_key "alert_histories", "users"
  add_foreign_key "alert_logs", "alerts"
  add_foreign_key "alert_logs", "users"
  add_foreign_key "alerts", "motels"
  add_foreign_key "alerts", "users"
  add_foreign_key "amenity_options", "amenities"
  add_foreign_key "announcements", "users", column: "created_by_id"
  add_foreign_key "caution_histories", "cautions"
  add_foreign_key "caution_histories", "users"
  add_foreign_key "cautions", "motels"
  add_foreign_key "cautions", "users"
  add_foreign_key "motel_amenities", "amenities"
  add_foreign_key "motel_amenities", "amenity_options"
  add_foreign_key "motel_amenities", "motels"
  add_foreign_key "motel_change_logs", "amenities"
  add_foreign_key "motel_change_logs", "amenity_options"
  add_foreign_key "motel_change_logs", "motels"
  add_foreign_key "motel_change_logs", "services"
  add_foreign_key "motel_change_logs", "users"
  add_foreign_key "motel_services", "motels"
  add_foreign_key "motel_services", "services"
  add_foreign_key "notification_preferences", "users"
  add_foreign_key "notifications", "alerts"
  add_foreign_key "notifications", "announcements"
  add_foreign_key "notifications", "cautions"
  add_foreign_key "notifications", "comments"
  add_foreign_key "notifications", "motels"
  add_foreign_key "notifications", "suggestions"
  add_foreign_key "notifications", "users"
  add_foreign_key "services", "organization_types"
  add_foreign_key "suggestion_client_cohorts", "client_cohorts"
  add_foreign_key "suggestion_client_cohorts", "suggestions"
  add_foreign_key "suggestion_histories", "suggestions"
  add_foreign_key "suggestion_histories", "users"
  add_foreign_key "suggestions", "motels"
  add_foreign_key "suggestions", "users"
end
