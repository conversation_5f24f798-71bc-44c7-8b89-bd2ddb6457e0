class CreateSuggestion < ActiveRecord::Migration[7.0]
  def change
    create_table :suggestions do |t|
      t.references :motel, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.text :description
      t.string :authorised_by
      t.string :user_organization
      t.string :user_position_title
      t.datetime :discarded_at

      t.timestamps
    end
  end
end
