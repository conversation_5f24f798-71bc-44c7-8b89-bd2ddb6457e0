class CreateAlerts < ActiveRecord::Migration[7.0]
  def change
    create_table :alerts do |t|
      t.references :user, null: false, foreign_key: true
      t.references :motel, null: false, foreign_key: true
      t.string :user_position_title
      t.string :user_organization
      t.string :alert_type
      t.date :date
      t.time :time
      t.text :location
      t.string :witnesses, default: [], array: true
      t.text :description
      t.datetime :closed_at
      t.string :closure_note
      t.string :title
      t.string :reported_by
      t.boolean :caution_issued, default: false

      t.timestamps
    end
  end
end
