class CreateMotels < ActiveRecord::Migration[7.0]
  def change
    create_table :motels do |t|
      t.string :name
      t.string :region
      t.string :street
      t.string :suburb
      t.integer :postcode
      t.string :density
      t.string :phone
      t.string :email
      t.timestamps
      t.string :motel_type
      t.string :state, default: "VIC"
      t.string :duration, default: [], array: true
      t.string :website
    end
  end
end
