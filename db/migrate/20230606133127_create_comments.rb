class CreateComments < ActiveRecord::Migration[7.0]
  def change
    create_table :comments do |t|
      t.text :body
      t.boolean :pinned, default: false
      t.boolean :archived, default: false
      t.bigint :user_id, null: false
      t.string :user_position_title, null: false
      t.bigint :motel_id, null: false

      t.timestamps
    end
    add_index :comments, :user_id
    add_index :comments, :motel_id
  end
end
