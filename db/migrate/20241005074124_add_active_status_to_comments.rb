class AddActiveStatusToComments < ActiveRecord::Migration[7.0]
  def change
    add_column :comments, :active_status, :integer
    add_column :comments, :approved_at, :datetime
    add_column :comments, :rejected_at, :datetime
    add_column :comments, :rejection_reason, :integer
    add_column :comments, :rejection_note, :text

    reversible do |direction|
      direction.up do
        Comment.find_each do |comment|
          comment.active_status = :approved
          comment.approved_at = comment.created_at
          comment.save!
        end
      end
    end
  end
end
