class CreateNotifications < ActiveRecord::Migration[7.0]
  def change
    create_table :notifications do |t|
      t.belongs_to :user, null: false, foreign_key: true

      t.references :motel, null: true, foreign_key: true
      t.references :alert, null: true, foreign_key: true
      t.references :caution, null: true, foreign_key: true
      t.references :suggestion, null: true, foreign_key: true
      t.references :comment, null: true, foreign_key: true

      t.boolean :is_read
      t.integer :notification_category

      t.timestamps
    end
  end
end
