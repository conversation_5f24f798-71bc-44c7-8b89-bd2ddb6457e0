class CreateCautions < ActiveRecord::Migration[7.0]
  def change
    create_table :cautions do |t|
      t.text :description
      t.datetime :discarded_at
      t.string :authorised_by
      t.string :user_organization
      t.string :user_position_title
      t.references :motel, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true

      t.timestamps
    end
  end
end
