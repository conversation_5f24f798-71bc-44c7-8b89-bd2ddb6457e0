class CreateNotificationPreferences < ActiveRecord::Migration[7.0]
  def change
    create_table :notification_preferences do |t|
      t.belongs_to :user, null: false, foreign_key: true
      t.integer :notification_event
      t.boolean :email_notification_allowed

      t.timestamps
    end

    add_index :notification_preferences, [:user_id, :notification_event], unique: true, name: 'index_notification_preferences_on_user_id_and_event'
  end
end
