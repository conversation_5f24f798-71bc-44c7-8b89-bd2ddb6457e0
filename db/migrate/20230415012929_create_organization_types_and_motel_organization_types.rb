class CreateOrganizationTypesAndMotelOrganizationTypes < ActiveRecord::Migration[7.0]
  def change
    create_table :organization_types do |t|
      t.string :name, null: false

      t.timestamps
    end

    create_table :motel_organization_types do |t|
      t.bigint :motel_id, null: false
      t.bigint :organization_type_id, null: false
      t.string :note

      t.timestamps
    end

    add_index :motel_organization_types, :motel_id
    add_index :motel_organization_types, :organization_type_id
    add_foreign_key :motel_organization_types, :motels
    add_foreign_key :motel_organization_types, :organization_types
  end
end
