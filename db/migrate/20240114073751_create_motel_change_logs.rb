class CreateMotelChangeLogs < ActiveRecord::Migration[7.0]
  def change
    create_table :motel_change_logs do |t|
      t.references :motel, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true

      t.references :amenity, null: true, foreign_key: true
      t.references :amenity_option, null: true, foreign_key: true
      t.references :service, null: true, foreign_key: true

      t.string :column_name
      t.string :column_value
      t.integer :activity_type # Enum

      t.datetime :performed_at

      t.timestamps
    end
  end
end
