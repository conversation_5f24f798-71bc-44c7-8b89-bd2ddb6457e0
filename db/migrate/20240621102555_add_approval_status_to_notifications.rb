class AddApprovalStatusToNotifications < ActiveRecord::Migration[7.0]
  def change
    add_column :notifications, :approval_status, :integer
    add_column :notifications, :event_group_key, :uuid

    # Notification.reset_column_information

    # Notification.find_each do |notification|
    #   notification.approval_status = :approved
    #   notification.event_group_key = SecureRandom.uuid
    #   notification.save!
    # end

    add_index :notifications, [:event_group_key, :user_id], unique: true
  end
end
