class ReorderUserAdminRoles < ActiveRecord::Migration[7.0]
  def up
    # Disabled for safety now that this has been migrated on production
    # User.where(role: 3).each do |user|
    #   user.update!(role: 4)
    # end
    # User.where(role: 2).each do |user|
    #   user.update!(role: 3)
    # end
  end

  def down
    # Disabled for safety now that this has been migrated on production
    # User.where(role: 3).each do |user|
    #   user.update!(role: 2)
    # end
    # User.where(role: 4).each do |user|
    #   user.update!(role: 3)
    # end
  end
end
