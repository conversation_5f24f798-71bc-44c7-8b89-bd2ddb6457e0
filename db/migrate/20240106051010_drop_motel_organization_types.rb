class DropMotelOrganizationTypes < ActiveRecord::Migration[7.0]
  def up
    drop_table :motel_organization_types
  end

  def down
    create_table :motel_organization_types do |t|
      t.string :note
      t.bigint :motel_id, null: false
      t.bigint :organization_type_id, null: false

      t.timestamps
    end

    add_index :motel_organization_types, :motel_id
    add_index :motel_organization_types, :organization_type_id

    add_foreign_key :motel_organization_types, :motels, column: :motel_id
    add_foreign_key :motel_organization_types, :organization_types, column: :organization_type_id
  end
end
