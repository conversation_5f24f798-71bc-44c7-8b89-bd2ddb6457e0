namespace :amenities do
  desc 'Add Accepts Single Males amenity with options and add to existing motels'
  task add_accepts_single_males_amenity: :environment do
    amenity = Amenity.create(name: 'Accepts Single Males', amenity_type: 'safety')

    options = [
      { name: 'Yes', display_text: 'Accepts Single Male Clients', icon: 'man', color: 'red' },
      { name: 'No', display_text: 'No Single Male Clients Accepted', icon: 'man', color: 'green' },
      { name: 'Unknown', display_text: 'Accepts Single Male Clients', icon: 'help', color: 'gray' }
    ]

    options.each do |option|
      amenity.amenity_options.create(option)
    end

    # add amenity to all existing motels with the "Unknown" option as default
    Motel.find_each do |motel|
      unknown_option = amenity.amenity_options.find_by(name: 'Unknown')
      motel_amenity = MotelAmenity.new(
        amenity: amenity,
        amenity_option: unknown_option,
        motel_id: motel.id
      )
      motel_amenity.save!
    end
  end

  task create_motel_characteristic_amenities: :environment do
    raise 'Not applicable to prod anymore' if Rails.env.production? # Already applied to prod

    amenity_type = 'characteristic'

    amenities_data = [
      {
        name: 'last_resort',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'fatigued_provider',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'registered_sex_offenders',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'persons_using_violence',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'families_with_children',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'child_safe_standards',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'heavily_utilised',
        options: ['yes', 'no', 'unknown']
      },
      {
        name: 'new_to_the_sector',
        options: ['yes', 'no', 'unknown']
      }
      # {
      #   name: 'newly_added_listing',
      #   options: ['yes', 'no']
      # }
    ]

    amenities_data.each do |amenity_data|
      amenity = Amenity.where(name: amenity_data[:name]).first_or_initialize
      amenity.assign_attributes(
        amenity_type: amenity_type
      )
      Amenity.transaction do
        amenity.save!

        amenity_data[:options].each do |option_key|
          amenity_option = amenity.amenity_options.where(name: option_key).first_or_initialize
          amenity_option.assign_attributes(
            display_text: option_key,
            icon: option_key,
            color: option_key
          )
          amenity_option.save!
        end

        # Add amenities to all existing motels with the "Unknown" option as default
        Motel.find_each do |motel|
          unknown_option = amenity.amenity_options.find_by(name: 'unknown')
          motel_amenity = MotelAmenity.where(
            amenity: amenity,
            motel_id: motel.id
          ).first_or_initialize

          motel_amenity.assign_attributes(
            amenity_option: unknown_option
          )
          motel_amenity.save!
        end
      end
    end
  end
end
