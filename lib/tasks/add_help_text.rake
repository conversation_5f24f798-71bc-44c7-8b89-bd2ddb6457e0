namespace :amenities do
  desc 'Add help_text to amenities'
  task add_help_text: :environment do
    amenities_data = {
      'Accepts Single Males' => '<i>Does the accommodation accept single male clients from the community services sector?</i>',
      'Out-of-Hours Booking' => '<i>Does the accommodation allow bookings out-of-hours, after reception is no longer staffed and/or administrative staff are not available?</i>',
      'Out-of-Hours Check-in' => '<i>Can a client check into the accommodation after the listed reception hours and/or standard check-in times?</i>',
      'Safe Room Access' => '<i>“Safe room access” is referring to <u>Safe + Equal</u>
      guidance, which states that accommodation rooms should not face directly onto the street. It recommends that “wherever possible, rooms are accessible only via secure lift or after passing staffed reception as this provides additional security”.</i>',
      'Kitchen (Private)' => '<i>A “kitchen” contains all main amenities including a stove, oven, microwave, and fridge (ideally full-sized). A “kitchenette” is lacking a stove and oven, but includes other culinary amenities such as a microwave, toaster, kettle, hot plate, and at minimum a bar fridge. If a accommodation only has tea/coffee making facilities (kettle and bar fridge), this is not considered to be a “kitchen” or a “kitchenette” – please select “No”. If some rooms contain different kitchen facilities/some have no kitchen, please specify in the "Note" area.</i>',
      'Laundry' => '<i>If selecting “Yes”, please indicate if the laundry is in-room or communal.</i>',
      'Smoking Area' => '<i>Not all accommodation allow smoking on private balconies and/or courtyards attached to the room. Not all accommodation allow smoking anywhere on the premises, and do not have allocated communal outdoor areas for smokers.</i>',
      'On-Site Parking' => '<i>If selecting “Yes”, please indicate if the on-site parking is undercover, gated and/or secure.</i>',
      'Other Accessibility Options' => '<i>In this section, include details on non-wheelchair options such as braille signage, grab rails in the shower, elevator access to rooms for clients with limited mobility, or any other accessibility features that are not wheelchair-specific.</i>',
      'Pet Friendly' => '<i>If the accommodation charges a fee relating to the inclusion of pets in the booking, please detail these costs in the “note” section. Please be aware that accommodation providers are legally required to accept  registered service animals into accommodation with clients who have a disability necessitating them. Select "No" if the accommodation will only accept registered service animals, as this is the legal default policy for all providers under the Disability Discrimination Act (1992).</i>',
      'Voucher Allocation' => '<i>This section is referring to EA providers that will supply pre-paid supermarket vouchers to clients upon request from the booking organisation (whose staff have purchased the vouchers). If this arrangement has not been agreed to by the provider in advance, please select “No”.</i>',
      'ID Required' => '<i>If the accommodation has requested ID for some clients but not others, please select “Yes (Sometimes)”. If the accommodation always requires ID, select “Yes (Always)”. If selecting “No”, please ensure that the accommodation does not require ID as a rule, at all times. When it comes to attention that accommodation listed under “No ID required” does begin requesting ID off some/all clients, please ensure to edit these listings as soon as possible, adding details into the “Note” area.</i>',
    }

    amenities_data.each do |name, help_text|
      amenity = Amenity.find_by(name: name)
      next if amenity.nil?

      amenity.update(help_text: help_text)
      puts "Added help_text to #{amenity.name}"
    end
  end
end
