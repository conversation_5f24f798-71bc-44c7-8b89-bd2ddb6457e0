namespace :populate do
  desc 'Populate client cohorts'
  task client_cohorts: :environment do
    client_cohorts = [
      { name: 'AOD users' },
      { name: 'Culturally and linguistically diverse (CALD)' },
      { name: 'Disabled (both age-related and lifelong disabilities)' },
      { name: 'Experiencing complex mental health issues' },
      { name: 'Families with young children' },
      { name: 'From the adult services industry' },
      { name: 'Youth (aged 18 – 30)' },
      { name: 'Elder<PERSON> (aged 55+)' },
      { name: 'Female victim-survivors' },
      { name: 'Families with children who have additional needs' },
      { name: 'Indigenous Australian' },
      { name: 'LGBT+' },
      { name: 'Men who use violence', red: true },
      { name: 'Refugees and asylum seekers' },
      { name: 'Single male clients', red: true },
      { name: 'Women exiting prison' }
    ]

    client_cohorts.each do |cohort|
      ClientCohort.find_or_create_by(cohort)
    end

    puts "Created #{client_cohorts.size} client cohorts."
  end
end
