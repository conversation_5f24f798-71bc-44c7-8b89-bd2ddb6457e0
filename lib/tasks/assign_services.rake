namespace :motels do
  desc "Assign random services to motels based on associated motel organizations"
  task assign_services: :environment do
    puts "Starting to assign services to motels..."

    # Iterate through all motels
    Motel.find_each do |motel|
      # Skip this motel if there are already some associated motel_services
      if motel.motel_services.any?
        puts "Motel '#{motel.name}' already has associated services; skipping assignment"
        next
      end

      # Get all organization types associated with this motel
      organization_types = motel.organization_types

      # Get all services for those organization types
      services = Service.where(organization_type: organization_types)

      # Randomly select a service from the list, only if there are services available
      random_service = services.sample

      if random_service
        # Create a motel_service entry associating the motel and randomly selected service
        MotelService.create!(motel: motel, service: random_service)
        puts "Assigned service '#{random_service.name}' to motel '#{motel.name}'"
      else
        puts "No services found for motel '#{motel.name}'; skipping assignment"
      end
    end

    puts "Completed assigning services to motels."
  end
end
