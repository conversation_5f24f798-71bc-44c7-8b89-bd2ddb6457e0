namespace :services do
  desc "Search for specific OrganizationTypes and create associated services"
  task create_services: :environment do
    # Define organization types and their associated services
    organizations_and_services = [
      {
        name: 'Access Point/IAP',
        services: [
          'Haven Home Safe access point (North East)',
          'Launch Housing access points (North and Inner South)',
          'Merri Outreach Support Service – Crisis Support',
          'Salvation Army Western Metro IAP',
          'Unison Housing IAP (Inner West)',
          'Vincentcare IAP (North)',
          'Salvation Army St Kilda Crisis Centre'
        ]
      },
      {
        name: 'Local FV Support Service',
        services: [
          'Australian Muslim Women\'s Centre for Human Rights',
          'Berry Street Northern Specialist Family Violence Service',
          'CoHealth Family Violence Service',
          'Drummond Street Services – Queerspace',
          'Elizabeth Morgan House',
          'GenWest',
          'Georgina Martina Inc.',
          'Good Samaritan Inn',
          'Good Shepherd',
          'InTouch',
          'Juno Inc.',
          'Refuge Victoria',
          'Salvation Army Family Violence Service (North West)',
          'Uniting Integrated Family Violence Program (North)',
          'Victorian Aboriginal Child Care Agency',
          'Women\'s Liberation Halfway House'
        ]
      },
      {
        name: 'PUV/Justice Service',
        services: [
          'Corrections Victoria',
          'Uniting Perpetrator Accommodation Support Program',
          'Western Health'
        ]
      },
      {
        name: 'Other (Books EA for Males/Perpetrators)',
        services: [
          'Asylum Seeker Resource Centre',
          'Kirrip Aboriginal Corporation',
          'Melbourne City Mission',
          'Victorian Aboriginal Community Services Association Ltd'
        ]
      },
      {
        name: 'Other (Books EA for Females/Victim-Survivors)',
        services: []
      },
      {
        name: 'The Orange Door',
        services: [
          'The Orange Door Brimbank Melton',
          'The Orange Door Hume Merri-bek',
          'The Orange Door North-East Melbourne',
          'The Orange Door Western Melbourne'
        ]
      },
      {
        name: 'Safe Steps',
        services: [
          'Safe Steps'
        ]
      }
    ]

    # Iterate through organization types and create associated services
    organizations_and_services.each do |organization_and_services|
      organization_type = OrganizationType.find_by(name: organization_and_services[:name])

      if organization_type
        organization_and_services[:services].each do |service_name|
          Service.create(name: service_name, organization_type: organization_type)
        end

        puts "Created services for '#{organization_and_services[:name]}'."
      else
        puts "Organization type '#{organization_and_services[:name]}' not found."
      end
    end
  end
end
