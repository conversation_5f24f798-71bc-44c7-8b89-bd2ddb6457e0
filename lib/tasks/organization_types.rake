namespace :organization_types do
  desc 'Add predefined organization types'
  task add_predefined: :environment do
    organization_type_names = [
      "Access Point/IAP",
      "Local FV Support Service",
      "The Orange Door",
      "PUV/Justice Service",
      "Salvation Army St Kilda Crisis Centre",
      "Safe Steps",
      "Other (Books EA for Males/Perpetrators)",
      "Other (Books EA for Females/Victim-Survivors)",
      "Other (Books EA for All Genders)"
    ]

    organization_type_names.each do |name|
      OrganizationType.find_or_create_by!(name: name)
    end

    puts "Predefined organization types added successfully."
  end
end
