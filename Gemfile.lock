GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.2)
    bcrypt (3.1.18)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    cancancan (3.5.0)
    concurrent-ruby (1.2.2)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.3.3)
    debug (1.7.1)
    devise (4.9.2)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.11.0)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.8)
    devise_invitable (2.0.8)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    discard (1.2.1)
      activerecord (>= 4.2, < 8)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    dry-auto_inject (1.0.1)
      dry-core (~> 1.0)
      zeitwerk (~> 2.6)
    dry-configurable (1.0.1)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.0)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    erubi (1.12.0)
    faker (3.2.0)
      i18n (>= 1.8.11, < 2)
    globalid (1.1.0)
      activesupport (>= 5.0)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.6.1)
    jwt (2.7.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    launchy (2.5.2)
      addressable (~> 2.8)
    letter_opener (1.8.1)
      launchy (>= 2.2, < 3)
    loofah (2.19.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mini_mime (1.1.2)
    minitest (5.18.0)
    msgpack (1.6.1)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.8)
    nokogiri (1.14.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-linux)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    parallel (1.21.0)
    parser (*******)
      ast (~> 2.4.1)
    pg (1.4.6)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    public_suffix (5.0.1)
    puma (5.6.5)
      nio4r (~> 2.0)
    racc (1.6.2)
    rack (*******)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.0.6)
    redcarpet (3.6.0)
    redis (4.8.1)
    regexp_parser (2.7.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.5)
    rollbar (3.5.2)
    rubocop (1.48.1)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= 3.2.0.0)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.26.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.27.0)
      parser (>= 3.2.1.0)
    ruby-progressbar (1.11.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    thor (1.2.1)
    timeout (0.3.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.4.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.8.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.7)

PLATFORMS
  arm64-darwin-20
  arm64-darwin-22
  arm64-darwin-23
  x86_64-linux

DEPENDENCIES
  annotate
  bootsnap
  cancancan
  debug
  devise
  devise-jwt
  devise_invitable
  discard (~> 1.2)
  dotenv-rails
  faker (~> 3.2)
  jbuilder
  kaminari (~> 1.2)
  letter_opener
  pg
  pg_search (~> 2.3, >= 2.3.6)
  puma (~> 5.0)
  rack-cors
  rails (~> 7.0.4, >= 7.0.4.2)
  redcarpet (~> 3.5)
  rollbar
  rubocop
  sidekiq (~> 6.5)

RUBY VERSION
   ruby 3.2.1p31

BUNDLED WITH
   2.4.6
