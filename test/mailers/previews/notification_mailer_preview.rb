class NotificationMailerPreview < ActionMailer::Preview
  def motel_created
    notification = assert_notification_exists(Notification.motel_created.last)
    NotificationMailer.with(notification: notification).created
  end

  def motel_updated
    notification = assert_notification_exists(Notification.motel_updated.last)
    NotificationMailer.with(notification: notification).created
  end

  def motel_deactivated
    notification = assert_notification_exists(Notification.motel_deactivated.last)
    NotificationMailer.with(notification: notification).created
  end

  def motel_reactivated
    notification = assert_notification_exists(Notification.motel_reactivated.last)
    NotificationMailer.with(notification: notification).created
  end

  ###

  def alert_created
    notification = assert_notification_exists(Notification.alert_created.last)
    NotificationMailer.with(notification: notification).created
  end

  def alert_updated
    notification = assert_notification_exists(Notification.alert_updated.last)
    NotificationMailer.with(notification: notification).created
  end

  def alert_discarded
    notification = assert_notification_exists(Notification.alert_discarded.last)
    NotificationMailer.with(notification: notification).created
  end

  ###

  def caution_created
    notification = assert_notification_exists(Notification.caution_created.last)
    NotificationMailer.with(notification: notification).created
  end

  def caution_updated
    notification = assert_notification_exists(Notification.caution_updated.last)
    NotificationMailer.with(notification: notification).created
  end

  def caution_discarded
    notification = assert_notification_exists(Notification.caution_discarded.last)
    NotificationMailer.with(notification: notification).created
  end

  def caution_undiscarded
    notification = assert_notification_exists(Notification.caution_undiscarded.last)
    NotificationMailer.with(notification: notification).created
  end

  ###

  def suggestion_created
    notification = assert_notification_exists(Notification.suggestion_created.last)
    NotificationMailer.with(notification: notification).created
  end

  def suggestion_updated
    notification = assert_notification_exists(Notification.suggestion_updated.last)
    NotificationMailer.with(notification: notification).created
  end

  def suggestion_discarded
    notification = assert_notification_exists(Notification.suggestion_discarded.last)
    NotificationMailer.with(notification: notification).created
  end

  def suggestion_undiscarded
    notification = assert_notification_exists(Notification.suggestion_undiscarded.last)
    NotificationMailer.with(notification: notification).created
  end

  ###

  def comment_review_required
    notification = assert_notification_exists(Notification.comment_review_required.last)
    NotificationMailer.with(notification: notification).created
  end

  def comment_created
    notification = assert_notification_exists(Notification.comment_created.last)
    NotificationMailer.with(notification: notification).created
  end

  def comment_updated
    notification = assert_notification_exists(Notification.comment_updated.last)
    NotificationMailer.with(notification: notification).created
  end

  def comment_discarded
    notification = assert_notification_exists(Notification.comment_discarded.last)
    NotificationMailer.with(notification: notification).created
  end

  def comment_undiscarded
    notification = assert_notification_exists(Notification.comment_undiscarded.last)
    NotificationMailer.with(notification: notification).created
  end

  ###

  def announcement_created
    notification = assert_notification_exists(Notification.announcement_created.last)
    NotificationMailer.with(notification: notification).created
  end

  private

  def assert_notification_exists(notification)
    raise 'Notification not found' unless notification
    notification
  end
end
