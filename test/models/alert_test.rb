# == Schema Information
#
# Table name: alerts
#
#  id                         :bigint           not null, primary key
#  alert_type                 :string
#  caution_issued             :boolean          default(FALSE)
#  closed_at                  :datetime
#  closed_user_organization   :string
#  closed_user_position_title :string
#  closure_note               :string
#  date                       :date
#  description                :text
#  discarded_at               :datetime
#  location                   :text
#  reported_by                :string
#  time                       :time
#  user_organization          :string
#  user_position_title        :string
#  witnesses                  :string           default([]), is an Array
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  closed_user_id             :bigint
#  motel_id                   :bigint           not null
#  user_id                    :bigint           not null
#
# Indexes
#
#  index_alerts_on_closed_user_id  (closed_user_id)
#  index_alerts_on_discarded_at    (discarded_at)
#  index_alerts_on_motel_id        (motel_id)
#  index_alerts_on_user_id         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (motel_id => motels.id)
#  fk_rails_...  (user_id => users.id)
#
require "test_helper"

class AlertTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
