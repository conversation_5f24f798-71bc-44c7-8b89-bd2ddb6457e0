# == Schema Information
#
# Table name: alert_histories
#
#  id                  :bigint           not null, primary key
#  description         :string
#  user_organization   :string
#  user_position_title :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  alert_id            :bigint           not null
#  user_id             :bigint           not null
#
# Indexes
#
#  index_alert_histories_on_alert_id  (alert_id)
#  index_alert_histories_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (alert_id => alerts.id)
#  fk_rails_...  (user_id => users.id)
#

one:
  alert: one
  user: one
  user_position_title: MyString
  user_organization: MyString
  description: MyString

two:
  alert: two
  user: two
  user_position_title: MyString
  user_organization: MyString
  description: MyString
